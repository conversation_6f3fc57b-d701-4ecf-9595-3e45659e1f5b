﻿"""
API Server for Jetty Planning Optimizer

This module provides an API interface for the jetty planning optimizer using FastAPI.
"""

import logging
import os
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from pathlib import Path
from dotenv import load_dotenv

from fastapi import FastAPI, HTTPException, Query, Depends, BackgroundTasks, Request
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import mimetypes
import uvicorn
from pydantic import BaseModel

# Scheduler imports
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor

from ..models.terminal import Terminal, Jetty, LoadingArm, LoadingArmType, Pump, Tank, Surveyor
from ..models.vessel import Vessel, Barge, VesselType, Cargo
from ..models.schedule import Schedule, Assignment
from ..models.nomination import Nomination
from ..optimization.scheduler import JettyScheduler
from ..integration.aisstream_client import AISStreamClient
from ..integration.openmeteo_api import OpenMeteoApiClient
from ..services.ship_tracking_service import ShipTrackingService, ShipPosition, TrackedShip
from ..services.tidal_service import TidalService
from ..services.lock_service import LockService
# Test data generation removed for production
from ..llm_interface.claude_interface import ClaudeInterface
from ..database import Database

# Add imports for status utilities
from ..utils.status_utils import normalize_status, is_valid_vessel_status, is_valid_assignment_status

# Import assignment status job
from ..services.assignment_status_job import AssignmentStatusJob

# Import API routers
from .status_api import router as status_router
from .terminal_api import router as terminal_router, backward_compat_router
from .ml_api import router as ml_router
from .assignment_api import router as assignment_router

# Load environment variables early from .env if present (do not override existing)
try:
    env_path = Path('.') / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=False)
except Exception:
    # Safe best-effort; do not block app import on dotenv issues
    pass

logger = logging.getLogger(__name__)

# Initialize database
db = Database()


def _generate_unique_nomination_vessel_id(state: Dict) -> str:
    """Generate a unique vessel ID for nominations that does not collide with DB vessels.
    Uses the prefix 'NV' to avoid conflicts with existing 'V###' IDs from the database.
    """
    import re

    existing_ids = set()

    # In-memory vessels
    for v in state.get("vessels", []):
        existing_ids.add(getattr(v, "id", ""))

    # Database vessels
    try:
        terminal_id = db.get_active_terminal_id()
        if terminal_id:
            for v in db.get_vessels(terminal_id):
                vid = v.get("id")
                if vid:
                    existing_ids.add(vid)
    except Exception:
        # If DB access fails, continue with in-memory uniqueness
        pass

    # Also check nominations in database for unique ID generation
    try:
        terminal_id = db.get_active_terminal_id()
        if terminal_id:
            nominations = db.get_nominations(terminal_id) or []
            for nom in nominations:
                runtime_id = nom.get('runtime_vessel_id')
                if runtime_id:
                    existing_ids.add(str(runtime_id))
    except Exception:
        pass

    # Find next available NV number
    max_num = 0
    for vid in existing_ids:
        m = re.fullmatch(r"NV(\d{3,})", str(vid))
        if m:
            try:
                max_num = max(max_num, int(m.group(1)))
            except ValueError:
                continue

    next_id = f"NV{max_num + 1:03d}"
    logger.info(f"Generated unique vessel ID: {next_id} (existing IDs checked: {len(existing_ids)})")
    return next_id

# Initialize FastAPI app
app = FastAPI(
    title="Jetty Planning Optimizer API",
    description="API for optimizing jetty planning at EVOS petrochemical terminals",
    version="0.2.0"
)

# Get the directory of the current file
BASE_DIR = Path(__file__).resolve().parent.parent.parent
TEMPLATES_DIR = BASE_DIR / "src" / "templates"
STATIC_DIR = BASE_DIR / "src" / "static"
DATA_DIR = BASE_DIR / "data"

# Ensure correct MIME types for fonts
mimetypes.add_type('font/woff2', '.woff2')
mimetypes.add_type('font/ttf', '.ttf')

# Mount static files
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# Setup Jinja2 templates
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))


def render_template(request: Request, template_name: str, context: Optional[Dict[str, Any]] = None):
    """Render a template injecting CSP nonce from reverse proxy header.

    Expects the reverse proxy (nginx) to pass X-CSP-Nonce with a per-request value.
    """
    base_context: Dict[str, Any] = {
        "request": request,
        "nonce": request.headers.get("X-CSP-Nonce", ""),
    }
    if context:
        base_context.update(context)
    return templates.TemplateResponse(template_name, base_context)

# Critical endpoints will be added after get_data function is defined

# Add startup event for AIS preloading and assignment status job
@app.on_event("startup")
async def startup_event():
    """Initialize application state and start AIS preloading and assignment status job on startup"""
    global scheduler, assignment_status_job
    
    logger.info("FastAPI application starting up...")
    
    try:
        # Initialize application state
        state = await get_data()
        
        # Optionally start AIS preloading (configurable)
        aisstream = state.get("aisstream")
        if aisstream and not aisstream.use_mock:
            preload = False
            # Read from DB setting first
            try:
                preload_setting = db.get_setting('ais_preload')
                if preload_setting:
                    val = str(preload_setting.get('value', '')).lower()
                    preload = val in ("1", "true", "yes")
            except Exception:
                pass
            # Environment variable override
            if os.getenv('AIS_PRELOAD', '').lower() in ("1", "true", "yes"):
                preload = True

            if preload:
                # Preload with both static and position reports (include Class B variants)
                # Keeping the bbox tight limits volume while ensuring positions are available from the start.
                try:
                    aisstream.set_message_types([
                        "PositionReport",
                        "ExtendedClassBPositionReport",
                        "StandardClassBPositionReport",
                        "ShipStaticData",
                    ])
                except Exception:
                    pass
                logger.info("AIS preload enabled - starting continuous streaming with positions + static data")
                aisstream.start_streaming_continuous()
            else:
                logger.info("AIS preload disabled - streaming will start on-demand during searches")
        else:
            logger.info("AIS Stream in mock mode - no preloading needed")
        
        # Initialize assignment status job scheduler
        try:
            # Create assignment status job
            assignment_status_job = AssignmentStatusJob(db)
            
            # Create and configure scheduler
            if assignment_status_job.enabled:
                executors = {
                    'default': ThreadPoolExecutor(max_workers=1)
                }
                scheduler = BackgroundScheduler(executors=executors)
                
                # Get interval from environment variable (default: 60 seconds)
                interval_seconds = int(os.getenv('ASSIGNMENT_STATUS_CRON_INTERVAL_SEC', '60'))
                
                # Schedule the assignment status transition job
                scheduler.add_job(
                    func=assignment_status_job.run_transition_job,
                    trigger='interval',
                    seconds=interval_seconds,
                    id='assignment_status_transitions',
                    name='Assignment Status Transitions',
                    replace_existing=True
                )
                
                # Start the scheduler
                scheduler.start()
                logger.info(f"Assignment status transition job started with {interval_seconds}s interval")
            else:
                logger.info("Assignment status transition job is disabled")
                
        except Exception as e:
            logger.error(f"Error initializing assignment status job: {e}")
            # Don't fail startup, just log the error
        
    except Exception as e:
        logger.error(f"Error during startup initialization: {e}")
        # Don't fail startup, just log the error


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    global scheduler
    
    logger.info("FastAPI application shutting down...")
    
    try:
        # Shutdown scheduler gracefully
        if scheduler and scheduler.running:
            logger.info("Shutting down assignment status job scheduler...")
            scheduler.shutdown(wait=True)
            logger.info("Assignment status job scheduler shut down")
    except Exception as e:
        logger.error(f"Error during scheduler shutdown: {e}")

# Add routers after critical endpoints to avoid conflicts
app.include_router(status_router)
app.include_router(terminal_router)
app.include_router(ml_router)
app.include_router(assignment_router)
app.include_router(backward_compat_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global state
global_state = {
    "terminal": None,
    "vessels": [],
    "schedule": None,
    "nominations": None,
    "aisstream": None,
    "weather_api": None,
    "claude_interface": None,
    "optimization_in_progress": False,
    "last_optimization_time": None,
    "settings": {
        "terminal_name": "Port Terminal Alpha",
        "terminal_location": "Rotterdam, Netherlands",
        "timezone": "Europe/Amsterdam",
        "date_format": "DD/MM/YYYY",
        "language": "en",
        "dark_mode": False,
        "auto_refresh": True,
        "refresh_interval": 5,  # minutes
        "aisstream_api_key": ""
    }
}

# Global scheduler and assignment status job
scheduler: Optional[BackgroundScheduler] = None
assignment_status_job: Optional[AssignmentStatusJob] = None


# Models for API requests and responses
class OptimizationParameters(BaseModel):
    """Parameters for optimization request"""
    horizon_days: int = 7
    time_granularity_hours: int = 1
    weight_throughput: float = 10.0
    weight_demurrage: float = 5.0
    weight_priority: float = 3.0
    weight_weather: float = 2.0
    # Optional jetty balancing term (soft), 0 disables
    weight_jetty_balance: float = 0.0
    force_assign_all: bool = False
    include_mock_assignments: bool = False
    # Call-in modeling
    approach_time_hours: int = 2
    free_wait_buffer_hours: int = 1
    # Demo helper: greedily fill remaining feasible vessels after solver
    fill_unassigned: bool = False
    # Locking/selective enhancements
    preserve_locked: bool = True
    vessel_filter: Optional[List[str]] = None
    time_window_start: Optional[datetime] = None
    time_window_end: Optional[datetime] = None


class OptimizationResponse(BaseModel):
    """Response for optimization request"""
    success: bool
    message: str
    objective_value: Optional[float] = None
    schedule_id: Optional[str] = None
    assignment_count: Optional[int] = None


class ImpactPreviewResponse(BaseModel):
    """Impact preview summary before running optimization"""
    success: bool
    message: str
    locked_preserved: int = 0
    unlocked_candidates: int = 0
    vessels_considered: int = 0
    time_window: Optional[Dict[str, str]] = None
    estimated_improvement: Optional[Dict[str, float]] = None


class AssignmentUpdate(BaseModel):
    """Update for a schedule assignment"""
    jetty_id: Optional[str] = None
    vessel_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: Optional[str] = None
    notes: Optional[str] = None


class AssistantRequest(BaseModel):
    """Request for the assistant API"""
    message: str


class NewNominationRequest(BaseModel):
    """Request model for creating a new nomination"""
    name: str
    vessel_type: str  # TANKER, BARGE, etc.
    length: float
    beam: float
    draft: float
    deadweight: float
    eta: Optional[datetime] = None
    etd: Optional[datetime] = None
    customer: Optional[str] = None
    priority: int = 0
    capacity: float = 0.0
    width: float = 0.0
    cargoes: List[Dict] = []  # List of cargo dictionaries
    
    # Vessel-specific fields
    cargo_type: Optional[str] = None
    flag: Optional[str] = None
    imo: Optional[str] = None
    mmsi: Optional[str] = None
    customs_cleared: bool = False
    last_port: Optional[str] = None
    next_port: Optional[str] = None
    
    # Barge-specific fields
    owner: Optional[str] = None
    registration_number: Optional[str] = None
    tug_boat: Optional[str] = None
    operation_type: Optional[str] = None
    has_crane: bool = False
    
    # Administrative fields
    submitted_by: Optional[str] = None
    metadata: Dict[str, Any] = {}








# Dependency to get current state
async def get_data() -> Dict:
    """Get the current state of the application"""
    # Production mode: Initialize fresh state from database only
    # Only initialize if terminal or vessels are actually missing (None), not just empty
    should_initialize = global_state.get("terminal") is None or global_state.get("vessels") is None

    if should_initialize:
        logger.info("Initializing production application state from database...")
        
        # Get terminal from database as proper Terminal object
        try:
            terminal_id = db.get_active_terminal_id()
            if terminal_id:
                # Import the extension to add the method
                try:
                    import src.database_extension
                except ImportError:
                    pass
                
                # Try to get terminal as object first
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object(terminal_id)
                    if terminal_obj:
                        global_state["terminal"] = terminal_obj
                        logger.info(f"Loaded terminal object from database: {terminal_obj.name}")
                    else:
                        logger.warning(f"Could not create terminal object for {terminal_id}")
                        global_state["terminal"] = None
                else:
                    # Fallback to dictionary approach
                    terminal_data = db.get_terminal(terminal_id)
                    if terminal_data:
                        global_state["terminal"] = terminal_data
                        logger.info(f"Loaded terminal data from database: {terminal_data['name']}")
                    else:
                        logger.warning(f"Active terminal {terminal_id} not found in database")
                        global_state["terminal"] = None
            else:
                logger.warning("No active terminal ID found in database")
                global_state["terminal"] = None
        except Exception as e:
            logger.error(f"Error loading terminal from database: {e}")
            global_state["terminal"] = None
        
        # Initialize vessels list if it doesn't exist (preserve existing in-memory vessels)
        if "vessels" not in global_state:
            global_state["vessels"] = []
            logger.info("Initialized empty vessels list")
        
        # Get assignments/schedule from database
        try:
            terminal_id = db.get_active_terminal_id()
            if terminal_id:
                db_assignments = db.get_assignments(terminal_id)
            else:
                db_assignments = []
            # Convert to Schedule object
            global_state["schedule"] = Schedule(
                start_time=datetime.now(timezone.utc), 
                end_time=datetime.now(timezone.utc) + timedelta(days=7),
                assignments=db_assignments
            )
            if terminal_id:
                logger.info(f"Loaded {len(db_assignments)} assignments from database for terminal {terminal_id}")
            else:
                logger.warning("No active terminal found - no assignments loaded")
        except Exception as e:
            logger.error(f"Error loading assignments from database: {e}")
            global_state["schedule"] = Schedule(
                start_time=datetime.now(timezone.utc), 
                end_time=datetime.now(timezone.utc) + timedelta(days=7),
                assignments=[]
            )
        
        # Note: Nominations are now converted directly to vessels, no nominations list needed
        
        global_state["original_schedule"] = None
        logger.info("Production state initialized from database")
        
        # Initialize other state components if they are missing
        if not global_state.get("aisstream"):
            # Prefer environment variable, then database, then TEST
            env_key = os.getenv('AISSTREAM_API_KEY')
            if env_key:
                aisstream_key = env_key
                logger.info("Using AIS Stream API key from environment variable")
            else:
                aisstream_setting = db.get_setting('aisstream_api_key')
                if aisstream_setting and aisstream_setting.get('value'):
                    aisstream_key = aisstream_setting['value']
                    logger.info("Using AIS Stream API key from database")
                else:
                    aisstream_key = 'TEST'
                    logger.info("No AIS Stream API key found - using mock mode")
            
            global_state["aisstream"] = AISStreamClient(api_key=aisstream_key)
            # Constrain AIS region to active terminal radius (server-side bbox + client-side filter)
            try:
                terminal_for_bbox = global_state.get("terminal")
                if terminal_for_bbox and hasattr(terminal_for_bbox, 'location'):
                    lat, lon = terminal_for_bbox.location
                else:
                    # Default to EVOS Terneuzen
                    lat, lon = (51.34543250288062, 3.751466718019277)
                # Default preload radius 100 km (fits Antwerp)
                try:
                    setting = db.get_setting('ais_radius_km')
                    radius_km = float(setting['value']) if setting and setting.get('value') else 100.0
                except Exception:
                    radius_km = 100.0
                global_state["aisstream"].set_subscription_region(lat, lon, radius_km=radius_km)
                # Enable liquid-only pre-collection filter by default for petrochemical terminal
                try:
                    only_liquid_setting = db.get_setting('ais_only_liquid')
                    enable_only_liquid = True if only_liquid_setting is None else bool(only_liquid_setting.get('value', True))
                except Exception:
                    enable_only_liquid = True
                try:
                    global_state["aisstream"].set_only_liquid_filter(enable_only_liquid)
                except Exception:
                    pass
            except Exception as e:
                logger.warning(f"Could not set AIS subscription region: {e}")
            # Don't auto-start streaming - will start on-demand when searching
            logger.info("AIS Stream client ready (will start on-demand during vessel search)")
        
        if not global_state.get("weather_api"):
             global_state["weather_api"] = OpenMeteoApiClient()
        
        if not global_state.get("claude_interface"):
            # Try environment variable first, then database, then default to TEST
            claude_key = os.getenv('ANTHROPIC_API_KEY')
            if claude_key:
                logger.info("Using Anthropic API key from environment variable")
            else:
                claude_api_key = db.get_setting('claude_api_key')
                claude_key = claude_api_key['value'] if claude_api_key else 'TEST'
                if claude_key != 'TEST':
                    logger.info("Using Claude API key from database")
                else:
                    logger.info("No Claude API key found - using mock mode")
            
            global_state["claude_interface"] = ClaudeInterface(api_key=claude_key)
        
        # Initialize ship tracking service
        if not global_state.get("ship_tracking"):
            terminal = global_state.get("terminal")
            if terminal and hasattr(terminal, 'location'):
                terminal_location = terminal.location
            else:
                # Default to EVOS Terneuzen terminal coordinates
                terminal_location = (51.34543250288062, 3.751466718019277)
            
            global_state["ship_tracking"] = ShipTrackingService(terminal_location)
            logger.info(f"Ship tracking service initialized for terminal at {terminal_location}")
        
        # Initialize tidal service
        if not global_state.get("tidal_service"):
            global_state["tidal_service"] = TidalService()
            logger.info("Tidal service initialized with RWS integration")
        
        # Initialize lock service  
        if not global_state.get("lock_service"):
            global_state["lock_service"] = LockService()
            logger.info("Lock service initialized with RWS WFS integration")
        
        global_state["optimization_in_progress"] = False
        global_state["last_optimization_time"] = None

    # Ensure schedule is initialized even if loading persistent data failed partially
    if not global_state.get("schedule"):
        global_state["schedule"] = Schedule(start_time=datetime.now(timezone.utc), end_time=datetime.now(timezone.utc) + timedelta(days=7))
    
    return global_state


# Utility endpoint to refresh WFS cache for locks
@app.post("/api/locks/refresh")
async def refresh_locks(state: Dict = Depends(get_data)):
    lock_service = state.get("lock_service")
    if not lock_service:
        raise HTTPException(status_code=503, detail="Lock service not available")
    try:
        await lock_service._ensure_wfs_loaded()
        return {"success": True, "total_locks": len(lock_service.locks)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to refresh locks: {str(e)}")

# Debug endpoint to get sample vessel names for testing
@app.get("/api/vessels/samples")
async def get_sample_vessels(state: Dict = Depends(get_data)):
    """Get sample vessel names for testing"""
    aisstream = state["aisstream"]
    
    if aisstream.use_mock:
        return {"vessels": ["Test Vessel 1", "Test Vessel 2"], "source": "mock"}
    
    # Get first 10 vessels with names from AIS data
    sample_vessels = []
    count = 0
    for mmsi, vessel_data in aisstream.vessel_data.items():
        if count >= 10:
            break
        
        vessel_name = vessel_data.get("name", "").strip()
        if vessel_name and vessel_name != "Unknown":
            sample_vessels.append({
                "name": vessel_name,
                "mmsi": mmsi,
                "type": vessel_data.get("vessel_type", "Unknown"),
                "length": vessel_data.get("length"),
                "source": "ais_stream"
            })
            count += 1
    return {
        "vessels": sample_vessels,
        "count": len(sample_vessels),
        "total_tracked": len(aisstream.vessel_data),
        "source": "ais_stream"
    }

# Vessel search endpoint (positioned after get_data is defined)
@app.get("/api/vessels/search")
async def search_vessels_api(
    query: str = Query(..., description="Search term (vessel name, MMSI, or IMO)"),
    wait_for_data: bool = Query(False, description="Wait for AIS data if connection is starting"),
    only_liquid: bool = Query(False, description="Filter to liquid cargo ships (oil/chemical tankers, inland tankers)"),
    type_filter: Optional[str] = Query(None, description="Comma-separated substrings to match vessel type (case-insensitive) e.g. 'tanker,oil'"),
    disable_radius_filter: bool = Query(False, description="Bypass terminal radius filter (useful for explicit MMSI lookups)"),
    state: Dict = Depends(get_data)
):
    """Search vessels from AIS Stream data and manually added vessels"""
    aisstream = state["aisstream"]
    
    if not query or len(query.strip()) < 2:
        return {"vessels": [], "message": "Search query too short", "status": "error"}
    
    original_query = query.strip()
    query = original_query.lower()
    matching_vessels = []
    search_status = "ready"
    connection_info = {}
    
    # Check AIS Stream status and handle connection
    if not aisstream.use_mock:
        vessel_count = len(aisstream.vessel_data)
        
        if not aisstream.connection_active:
            # AIS not connected - start it if not already starting
            logger.info("AIS not connected, starting on-demand for vessel search")
            # Keep the connection short to limit message volume; extended data builds over repeated searches
            try:
                # Ensure we also receive live positions so maps can render (Class A and B)
                aisstream.set_message_types([
                    "PositionReport",
                    "ExtendedClassBPositionReport",
                    "StandardClassBPositionReport",
                    "ShipStaticData",
                ])
            except Exception:
                pass
            # If caller explicitly bypasses radius filter with a 9-digit MMSI query,
            # use server-side MMSI filters and relax the bounding box so we actually
            # receive the vessel even when it's still far away.
            try:
                is_mmsi_query = original_query.isdigit() and len(original_query) == 9
                if disable_radius_filter and is_mmsi_query:
                    aisstream.set_mmsi_filters([original_query])
                    # World-wide bbox; server will still filter by MMSI
                    aisstream.bounding_boxes = [[-90.0, -180.0], [90.0, 180.0]]
            except Exception:
                pass
            aisstream.start_streaming_on_demand(duration_minutes=2)
            search_status = "connecting"
            connection_info = {
                "message": "Starting AIS connection to gather vessel data...",
                "estimated_wait_seconds": 20,
                "suggestion": "Search results will improve as data arrives"
            }
            
            # If wait_for_data is requested, wait a bit for initial data
            if wait_for_data:
                import asyncio
                await asyncio.sleep(3)  # Wait 3 seconds for initial connection
                search_status = "collecting"
                connection_info = {
                    "message": "Collecting vessel data from AIS stream...", 
                    "estimated_wait_seconds": 15,
                    "suggestion": "More vessels may appear in subsequent searches"
                }
                
        elif vessel_count == 0:
            # Connected but no data yet
            search_status = "collecting"
            connection_info = {
                "message": "AIS connected, gathering vessel data...",
                "estimated_wait_seconds": 10,
                "suggestion": "Vessel database is building, try again in a few seconds"
            }
        elif vessel_count < 50:
            # Connected with some data but still building
            search_status = "collecting"
            connection_info = {
                "message": f"Building vessel database ({vessel_count} vessels tracked so far)...",
                "estimated_wait_seconds": 5,
                "suggestion": "More vessels may appear as data continues to arrive"
            }
        else:
            # Good amount of data available
            search_status = "ready"
    
    # Build quick lookup maps from DB vessels to enrich AIS results (e.g., deadweight)
    db_vessels_by_imo = {}
    db_vessels_by_name = {}
    try:
        terminal_id_idx = db.get_active_terminal_id()
        if terminal_id_idx:
            for v in db.get_vessels(terminal_id_idx):
                imo_val = str(v.get("imo")) if v.get("imo") else None
                name_val = v.get("name")
                if imo_val:
                    db_vessels_by_imo[imo_val] = v
                if name_val:
                    db_vessels_by_name[name_val.strip().lower()] = v
    except Exception:
        # Non-fatal; enrichment is best-effort
        pass

    # Normalize type filters
    def _matches_type_filter(ais_type_text: Optional[str], ais_type_code: Optional[int] = None) -> bool:
        # Numeric code short-circuit: AIS tanker classes 80–89
        if only_liquid and ais_type_code is not None:
            try:
                code_val = int(ais_type_code)
                if 80 <= code_val <= 89:
                    return True
            except Exception:
                pass

        # Text-based evaluation
        if not ais_type_text:
            return False if (only_liquid or type_filter) else True
        t = str(ais_type_text).lower()
        # Built-in liquid filter including gas carriers
        if only_liquid and not ("tanker" in t or "oil" in t or "chemical" in t or "inland" in t or "liquid" in t or "gas" in t or "lng" in t or "lpg" in t):
            return False
        # Custom substrings
        if type_filter:
            terms = [s.strip().lower() for s in type_filter.split(',') if s.strip()]
            if terms and not any(term in t for term in terms):
                return False
        return True

    # 1. Search through AIS Stream vessel data (real-time vessels)
    for mmsi, vessel_data in aisstream.vessel_data.items():
        # Check if query matches name, MMSI, or IMO
        vessel_name = vessel_data.get("name", "").lower()
        vessel_mmsi = str(mmsi).lower()
        vessel_imo = str(vessel_data.get("imo", "")).lower()
        
        if (query in vessel_name or 
            query in vessel_mmsi or 
            query in vessel_imo or
            vessel_name.startswith(query)):
            
            # Apply type filter using both AIS type code and text when available
            vessel_type_text = vessel_data.get("vessel_type_text") or vessel_data.get("vessel_type")
            vessel_type_code = None
            try:
                raw_vt = vessel_data.get("vessel_type")
                if raw_vt is not None:
                    vessel_type_code = int(raw_vt)
            except Exception:
                vessel_type_code = None
            if not _matches_type_filter(vessel_type_text, vessel_type_code):
                continue

            # Prefer AIS-provided deadweight when available; otherwise enrich from DB
            enriched_deadweight = None
            imo_val = str(vessel_data.get("imo")) if vessel_data.get("imo") else None
            if imo_val and imo_val in db_vessels_by_imo:
                enriched_deadweight = db_vessels_by_imo[imo_val].get("deadweight")
            if enriched_deadweight is None:
                nm = vessel_data.get("name", "").strip().lower()
                if nm and nm in db_vessels_by_name:
                    enriched_deadweight = db_vessels_by_name[nm].get("deadweight")

            # Choose final deadweight: AIS value wins, else DB enrichment (may remain None)
            preferred_deadweight = vessel_data.get("deadweight") if vessel_data.get("deadweight") is not None else enriched_deadweight

            # Format vessel data for response
            formatted_vessel = {
                "mmsi": mmsi,
                "name": vessel_data.get("name", "Unknown"),
                "imo": vessel_data.get("imo"),
                "vessel_type": vessel_data.get("vessel_type"),
                "vessel_type_text": vessel_data.get("vessel_type_text"),
                "length": vessel_data.get("length"),
                "beam": vessel_data.get("width"),  # AIS uses width, we call it beam
                "draft": vessel_data.get("draft"),
                "deadweight": preferred_deadweight,
                "destination": vessel_data.get("destination"),
                "eta": vessel_data.get("eta"),
                "position": vessel_data.get("position"),
                "status": vessel_data.get("status"),
                "last_update": aisstream.last_update.get(mmsi).isoformat() if mmsi in aisstream.last_update else None,
                "source": "ais_stream"
            }
            matching_vessels.append(formatted_vessel)
    
    # 2. Search through manually added vessels in database
    try:
        terminal_id = db.get_active_terminal_id()
        if terminal_id:
            db_vessels = db.get_vessels(terminal_id)
            
            for vessel in db_vessels:
                # Skip if this vessel is already in AIS data (avoid duplicates)
                vessel_mmsi = vessel.get("mmsi", "")
                if vessel_mmsi and vessel_mmsi in aisstream.vessel_data:
                    continue
                
                # Check if query matches vessel data
                vessel_name = vessel.get("name", "").lower()
                vessel_imo = str(vessel.get("imo", "")).lower()
                vessel_mmsi_str = str(vessel_mmsi).lower()
                
                if (query in vessel_name or 
                    query in vessel_mmsi_str or 
                    query in vessel_imo or
                    vessel_name.startswith(query)):
                    
                    # Format vessel data for response
                    formatted_vessel = {
                        "id": vessel.get("id"),
                        "name": vessel.get("name", "Unknown"),
                        "imo": vessel.get("imo"),
                        "mmsi": vessel.get("mmsi"),
                        "vessel_type": vessel.get("type", vessel.get("vessel_type")),
                        "length": vessel.get("length"),
                        "beam": vessel.get("beam"),
                        "draft": vessel.get("draft"),
                        "deadweight": vessel.get("deadweight"),
                        "eta": vessel.get("eta"),
                        "etd": vessel.get("etd"),
                        "status": vessel.get("status"),
                        "customer": vessel.get("customer"),
                        "priority": vessel.get("priority"),
                        "source": "user_added"
                    }
                    matching_vessels.append(formatted_vessel)
    except Exception as e:
        logger.warning(f"Could not search database vessels: {e}")
    
    # 2.5 Apply terminal radius filter based on AIS_RADIUS_KM for any result with a position
    try:
        # Center: active terminal or default EVOS Terneuzen
        terminal = state.get("terminal")
        if terminal and hasattr(terminal, 'location'):
            center_lat, center_lon = terminal.location
        else:
            center_lat, center_lon = (51.34543250288062, 3.751466718019277)

        # Radius from env var first, then DB setting, default 100km
        import os, math
        radius_env = os.getenv('AIS_RADIUS_KM')
        if radius_env is not None and str(radius_env).strip() != "":
            radius_km = float(radius_env)
        else:
            try:
                setting = db.get_setting('ais_radius_km')
                radius_km = float(setting['value']) if setting and setting.get('value') else 100.0
            except Exception:
                radius_km = 100.0

        def haversine_km(lat1, lon1, lat2, lon2):
            R = 6371.0
            dlat = math.radians(lat2 - lat1)
            dlon = math.radians(lon2 - lon1)
            a = math.sin(dlat/2)**2 + math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            return R * c

        def within_radius(v):
            pos = v.get('position') or {}
            lat = pos.get('latitude')
            lon = pos.get('longitude')
            if lat is None or lon is None:
                # If no position, do not filter out (likely DB/manual entry)
                return True
            try:
                return haversine_km(center_lat, center_lon, float(lat), float(lon)) <= radius_km
            except Exception:
                return True

        # If explicitly searching by MMSI and caller requests it, keep the MMSI match even if outside radius
        if disable_radius_filter:
            is_mmsi_query = original_query.isdigit()
            if is_mmsi_query:
                matching_vessels = [v for v in matching_vessels if str(v.get('mmsi')) == original_query or within_radius(v)]
            else:
                matching_vessels = [v for v in matching_vessels if within_radius(v)]
        else:
            matching_vessels = [v for v in matching_vessels if within_radius(v)]
    except Exception:
        # Non-fatal: if filtering fails, return unfiltered results
        pass

    # Sort by source (AIS Stream first, then user-added) and name
    matching_vessels.sort(key=lambda x: (x.get("source") != "ais_stream", x.get("name", "")))
    
    # Limit results to top 20 (increased for better results)
    matching_vessels = matching_vessels[:20]
    
    # Build a helpful status message for the client
    total_tracked = len(aisstream.vessel_data)
    if len(matching_vessels) == 0:
        if search_status == "connecting":
            response_message = connection_info.get("message", "Starting AIS connection...")
        elif search_status == "collecting":
            response_message = connection_info.get("message", f"Gathering vessel data. {total_tracked} vessels tracked so far.")
        elif total_tracked == 0:
            response_message = "No AIS data available yet. Connection may be starting up."
        else:
            response_message = f"No vessels found matching '{query}' in {total_tracked} tracked vessels."
    else:
        if search_status != "ready":
            response_message = f"Found {len(matching_vessels)} vessel(s). More may appear as data arrives."
        else:
            response_message = f"Found {len(matching_vessels)} vessel(s) matching '{query}'."

    return {
        "vessels": matching_vessels,
        "count": len(matching_vessels),
        "query": query,
        "total_tracked": total_tracked,
        "status": search_status,
        "connection_info": connection_info or None,
        "message": response_message,
        "sources": {
            "ais_stream": len([v for v in matching_vessels if v.get("source") == "ais_stream"]),
            "user_added": len([v for v in matching_vessels if v.get("source") == "user_added"])
        }
    }


# API routes
@app.get("/")
async def root(request: Request, state: Dict = Depends(get_data)):
    """Root endpoint - renders the dashboard"""
    return render_template(request, "index.html")


@app.get("/schedule")
async def schedule_page(request: Request, state: Dict = Depends(get_data)):
    """Schedule page"""
    return render_template(request, "schedule.html")


@app.get("/tracking")
async def ship_tracking_page(request: Request):
    """Serve the ship tracking dashboard page"""
    return render_template(request, "nominated_vessels.html", {"title": "Nominated Vessels - Tracking"})

@app.get("/nominated-vessels")
async def nominated_vessels_page(request: Request):
    """Serve the nominated vessels ETA tracking page"""
    return render_template(request, "nominated_vessels.html", {"title": "Smart ETA Tracking"})


@app.get("/schedule/add")
async def add_assignment_page(request: Request, state: Dict = Depends(get_data)):
    """Add assignment page"""
    return render_template(request, "add_assignment.html")

@app.get("/schedule/edit/{assignment_id}")
async def edit_assignment_page(assignment_id: str, request: Request, state: Dict = Depends(get_data)):
    """Edit assignment page"""
    # For now, reuse the add_assignment template
    return render_template(request, "add_assignment.html", {"assignment_id": assignment_id})


@app.get("/vessels")
async def vessels_page(request: Request, state: Dict = Depends(get_data)):
    """Vessels page"""
    return render_template(request, "vessels.html")


# Terminal page route removed - functionality not essential for production


@app.get("/weather")
async def weather_page(request: Request, state: Dict = Depends(get_data)):
    """Weather page"""
    return render_template(request, "weather.html")


@app.get("/optimize")
async def optimize_page(request: Request, state: Dict = Depends(get_data)):
    """Optimization page"""
    return render_template(request, "optimize.html")


@app.get("/assistant")
async def assistant_page(request: Request, state: Dict = Depends(get_data)):
    """Assistant page for LLM interface"""
    return render_template(request, "assistant.html")


@app.get("/settings")
async def settings_page(request: Request, state: Dict = Depends(get_data)):
    """Settings page"""
    return render_template(request, "settings.html")


@app.get("/analytics")
async def analytics_page(request: Request, state: Dict = Depends(get_data)):
    """Analytics page"""
    return render_template(request, "analytics.html")


@app.get("/logs")
async def logs_page(request: Request, state: Dict = Depends(get_data)):
    """Logs page for viewing schedule change logs"""
    return render_template(request, "logs.html")


# Terminals comparison page removed - only using Terneuzen terminal

@app.get("/nomination")
async def nomination_page(request: Request):
    """Vessel nomination page with ML predictions"""
    return render_template(request, "nomination.html")


@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """Serve a favicon to avoid 404s when browsers request /favicon.ico"""
    return FileResponse(STATIC_DIR / "icons" / "favicon-32x32.png")





@app.get("/test-nomination")
async def test_nomination_page():
    """Debug page for nomination testing"""
    from fastapi.responses import FileResponse
    return FileResponse("test_nomination_debug.html")



@app.get("/api/jetties")
async def get_jetties_api(terminal_id: Optional[str] = None):
    """Get all jetties API endpoint"""
    try:
        # Use provided terminal_id or get active terminal
        if not terminal_id:
            terminal_id = db.get_active_terminal_id()
        
        # Validate terminal exists
        terminal_data = db.get_terminal(terminal_id)
        if not terminal_data:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        # Get jetties for the terminal
        jetties = db.get_jetties(terminal_id)
        
        return [{
            "id": jetty["id"],
            "name": jetty["name"],
            "type": jetty.get("type", "VESSEL"),  # Use 'type' instead of 'jetty_type'
            "max_length": jetty.get("max_length", 200.0),  # Default values for missing fields
            "max_draft": jetty.get("max_draft", 12.0),
            "max_deadweight": jetty.get("max_deadweight", 50000.0),
            "is_operational": bool(jetty.get("is_operational", True)),
            "max_flow_rate": jetty.get("max_flow_rate", 1000.0),
            "terminal_id": terminal_id,  # Use the parameter instead of database field
            "terminal_name": terminal_data["name"]
        } for jetty in jetties]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting jetties for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve jetties")


@app.get("/api/tanks")
async def get_tanks_api(state: Dict = Depends(get_data)):
    """Get all tanks API endpoint"""
    # Ensure terminal is proper Terminal object
    terminal = state["terminal"]
    if isinstance(terminal, dict) or not hasattr(terminal, 'tanks'):
        from ..api.ml_api import create_fallback_terminal
        terminal = create_fallback_terminal()
    
    return [{
        "id": tank.id,
        "name": tank.name,
        "capacity": tank.capacity,
        "current_volume": tank.current_volume,
        "product_type": tank.product_type,
        "is_floating_roof": tank.is_floating_roof,
        "is_operational": tank.is_operational,
        "connected_jetties": tank.connected_jetties if hasattr(tank, 'connected_jetties') else []
    } for tank in terminal.tanks]


@app.get("/api/pumps")
async def get_pumps_api(state: Dict = Depends(get_data)):
    """Get all pumps API endpoint"""
    # Ensure terminal is proper Terminal object
    terminal = state["terminal"]
    if isinstance(terminal, dict) or not hasattr(terminal, 'pumps'):
        from ..api.ml_api import create_fallback_terminal
        terminal = create_fallback_terminal()
    
    return [{
        "id": pump.id,
        "name": pump.name,
        "flow_rate": pump.flow_rate,
        "compatible_products": list(pump.compatible_products),
        "is_operational": pump.is_operational,
        "current_rate": getattr(pump, 'current_rate', 0),
        "connected_tanks": getattr(pump, 'connected_tanks', []),
        "connected_jetties": getattr(pump, 'connected_jetties', [])
    } for pump in terminal.pumps]


@app.get("/api/orders")
async def get_orders_api(state: Dict = Depends(get_data)):
    """Get all current orders API endpoint"""
    assignments = state.get("assignments", [])
    return [{
        "id": assignment.id,
        "status": assignment.status,
        "start_time": assignment.start_time.isoformat(),
        "end_time": assignment.end_time.isoformat(),
        "vessel": {
            "name": assignment.vessel.name,
            "type": assignment.vessel.vessel_type.value,
            "imo": assignment.vessel.imo_number
        },
        "cargo": {
            "product": assignment.vessel.cargo.product,
            "volume": assignment.vessel.cargo.volume,
            "is_loading": assignment.vessel.cargo.is_loading
        },
        "jetty": {
            "id": assignment.jetty.id,
            "name": assignment.jetty.name
        },
        "tank_ids": assignment.tank_ids,
        "pump_ids": assignment.pump_ids,
        "actual_start_time": assignment.actual_start_time.isoformat() if assignment.actual_start_time else None,
        "actual_end_time": assignment.actual_end_time.isoformat() if assignment.actual_end_time else None
    } for assignment in assignments]


@app.get("/api/vessels")
async def get_vessels_api(
    terminal_id: Optional[str] = None,
    status: List[str] = Query([]),
    vessel_type: Optional[str] = None,
    state: Dict = Depends(get_data)
):
    """Get all vessels API endpoint - includes both database vessels and in-memory vessels"""
    try:
        # Use provided terminal_id or get active terminal
        if not terminal_id:
            terminal_id = db.get_active_terminal_id()
        
        # Validate terminal exists
        terminal_data = db.get_terminal(terminal_id)
        if not terminal_data:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        all_vessels = []
        
        # Get vessels from database (now includes cargo summaries)
        db_vessels = db.get_vessels(terminal_id)
        for vessel in db_vessels:
            all_vessels.append({
                "id": vessel.get("id"),
                "name": vessel.get("name"),
                "type": vessel.get("type", "TANKER"),
                "status": vessel.get("status", "EN_ROUTE"),
                "length": vessel.get("length", 180.0),
                "beam": vessel.get("beam", 32.0),
                "draft": vessel.get("draft", 12.0),
                "deadweight": vessel.get("deadweight", 40000.0),
                # Standardized ETA fields
                "eta": vessel.get("eta"),
                "etd": vessel.get("etd"),
                "calculated_eta": vessel.get("calculated_eta"),
                "eta_confidence": vessel.get("eta_confidence", 50),
                "eta_source": vessel.get("eta_source", "user"),
                "actual_arrival": vessel.get("actual_arrival"),
                "actual_departure": vessel.get("actual_departure"),
                "current_jetty": vessel.get("current_jetty"),
                "customer": vessel.get("customer", "Test Customer"),
                "priority": vessel.get("priority", 1),
                "total_cargo_volume": vessel.get("total_cargo_volume", 0),
                "cargoes": vessel.get("cargoes", []),
                "terminal_id": terminal_id,
                "terminal_name": terminal_data["name"],
                "source": "database"
            })
        
        # Get vessels from in-memory state (e.g., created from nominations)
        # Track all vessel IDs to prevent duplicates
        existing_vessel_ids = {v.get("id") for v in all_vessels}
        
        # Add in-memory vessels first (these are the primary source for nominations)
        for vessel in state.get("vessels", []):
            if vessel.id not in existing_vessel_ids:
                all_vessels.append({
                    "id": vessel.id,
                    "name": vessel.name,
                    "type": vessel.vessel_type.value.upper(),
                    "status": vessel.status,
                    "length": vessel.length,
                "beam": vessel.beam,
                "draft": vessel.draft,
                "deadweight": vessel.deadweight,
                # Standardized ETA fields
                "eta": vessel.eta.isoformat() if vessel.eta else None,
                "etd": vessel.etd.isoformat() if vessel.etd else None,
                "calculated_eta": vessel.calculated_eta.isoformat() if vessel.calculated_eta else None,
                "eta_confidence": vessel.eta_confidence,
                "eta_source": vessel.eta_source,
                "actual_arrival": vessel.actual_arrival.isoformat() if vessel.actual_arrival else None,
                "actual_departure": vessel.actual_departure.isoformat() if vessel.actual_departure else None,
                "current_jetty": vessel.current_jetty,
                "customer": vessel.customer,
                "priority": vessel.priority,
                # Expose identifiers if present (often stored in metadata for nominations)
                "mmsi": getattr(vessel, "mmsi", None) or vessel.metadata.get("mmsi"),
                "imo": getattr(vessel, "imo", None) or vessel.metadata.get("imo"),
                "total_cargo_volume": vessel.total_cargo_volume(),
                "cargoes": [{
                    "product": cargo.product,
                    "volume": cargo.volume,
                    "is_loading": cargo.is_loading,
                    "tanks": cargo.tanks,
                    "surveyor_required": cargo.surveyor_required,
                    "completed_volume": cargo.completed_volume
                } for cargo in vessel.cargoes],
                "terminal_id": terminal_id,
                "terminal_name": terminal_data["name"],
                "source": "memory"
            })
                existing_vessel_ids.add(vessel.id)
        
        # Also rehydrate nominated vessels from DB if not already in memory
        try:
            nominated = db.get_nominations(terminal_id, status=None)
            for n in nominated:
                rid = n.get('runtime_vessel_id')
                if rid and rid not in existing_vessel_ids:
                    # Only add nominations that don't have corresponding in-memory vessels
                    all_vessels.append({
                        "id": rid,
                        "name": n.get('name'),
                        "type": n.get('vessel_type', 'TANKER'),
                        "status": "APPROACHING",
                        "length": n.get('length'),
                        "beam": n.get('beam'),
                        "draft": n.get('draft'),
                        "deadweight": n.get('deadweight'),
                        "customer": n.get('customer'),
                        "priority": n.get('priority', 0),
                        "mmsi": n.get('mmsi'),
                        "imo": n.get('imo'),

                        # Standardized ETA fields
                        "eta": n.get('eta'),
                        "etd": n.get('etd'),
                        "calculated_eta": n.get('calculated_eta'),
                        "eta_confidence": n.get('eta_confidence', 50),
                        "eta_source": n.get('eta_source', 'user'),
                        "actual_arrival": n.get('actual_arrival'),
                        "actual_departure": n.get('actual_departure'),

                        "total_cargo_volume": sum((c.get('volume') or 0) for c in (n.get('cargoes') or [])),
                        "cargoes": n.get('cargoes') or [],
                        "terminal_id": terminal_id,
                        "terminal_name": terminal_data["name"],
                        "source": "nominations"
                    })
                    existing_vessel_ids.add(rid)
        except Exception as e:
            logger.warning(f"Failed to rehydrate nominations: {e}")

        # Filter by status if provided
        if status:
            all_vessels = [v for v in all_vessels if v.get("status") in status]

        # Filter by vessel type if provided
        if vessel_type:
            all_vessels = [v for v in all_vessels if v.get("type") == vessel_type.upper()]

        logger.info(f"Retrieved {len(all_vessels)} vessels (filtered by status: {status})")
        return all_vessels
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting vessels for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve vessels")


@app.get("/api/vessels/{vessel_id}")
async def get_vessel_api(vessel_id: str, state: Dict = Depends(get_data)):
    """Get a specific vessel API endpoint"""
    vessel = next((v for v in state["vessels"] if v.id == vessel_id), None)
    if not vessel:
        raise HTTPException(status_code=404, detail=f"Vessel {vessel_id} not found")

    return {
        "id": vessel.id,
        "name": vessel.name,
        "type": vessel.vessel_type.value,
        "status": vessel.status,
        "length": vessel.length,
        "beam": vessel.beam,
        "draft": vessel.draft,
        "deadweight": vessel.deadweight,
        "eta": vessel.eta.isoformat() if vessel.eta else None,
        "etd": vessel.etd.isoformat() if vessel.etd else None,
        "arrival_time": vessel.arrival_time.isoformat() if vessel.arrival_time else None,
        "departure_time": vessel.departure_time.isoformat() if vessel.departure_time else None,
        "current_jetty": vessel.current_jetty,
        "customer": vessel.customer,
        "priority": vessel.priority,
        "cargoes": [{
            "product": cargo.product,
            "volume": cargo.volume,
            "is_loading": cargo.is_loading,
            "tanks": cargo.tanks,
            "surveyor_required": cargo.surveyor_required,
            "completed_volume": cargo.completed_volume
        } for cargo in vessel.cargoes]
    }


@app.get("/api/schedule")
async def get_schedule_api(state: Dict = Depends(get_data)):
    """Get the current schedule API endpoint"""
    schedule = state["schedule"]
    return {
        "start_time": schedule.start_time.isoformat(),
        "end_time": schedule.end_time.isoformat(),
        "assignment_count": len(schedule.assignments),
        "objective_value": schedule.objective_value,
        "utilization": schedule.get_jetty_utilization()
    }


@app.get("/api/schedule/assignments")
async def get_assignments_api(state: Dict = Depends(get_data)):
    """Proxy to DB-backed assignments endpoint in main app (compat)."""
    # To avoid duplicate logic and ensure persistence, rely on DB methods
    try:
        terminal_id = db.get_active_terminal_id()
        assignments = db.get_assignments(terminal_id)

        # Enrich with ML predictions for UI (non-persistent)
        try:
            from src.ml.prediction_service import MLPredictionService
            from src.ml.models import PredictionRequest
            from src.ml.feature_validator import MLFeatureValidator
            # Reuse singleton from state if available
            ml_service = state.get("ml_service") or MLPredictionService()
            terminal = state.get("terminal")
            vessels_state = {getattr(v, 'id'): v for v in state.get("vessels", [])}

            # Build jetty lookup by both id and name
            jetty_by_name = {}
            jetty_by_id = {}
            if terminal and hasattr(terminal, 'jetties'):
                for j in terminal.jetties:
                    jetty_by_name[getattr(j, 'name', None)] = j
                    jetty_by_id[getattr(j, 'id', None)] = j

            for a in assignments:
                vessel_id = a.get('vessel_id')
                
                # Debug logging for PostgreSQL migration issues
                if vessel_id == 'vessel_id' or not vessel_id:
                    logger.warning(f"Assignment {a.get('id')} has corrupted vessel_id: '{vessel_id}'")
                
                v = vessels_state.get(vessel_id)
                j = jetty_by_name.get(a.get('jetty_name')) or jetty_by_id.get(a.get('jetty_name'))
                
                # Log vessel lookup results for debugging
                if vessel_id and not v:
                    logger.debug(f"No vessel found in state for vessel_id '{vessel_id}' (assignment {a.get('id')})")

                # Enrich with cargo details from in-memory vessel if available
                try:
                    if v and getattr(v, 'cargoes', None):
                        total_volume = 0.0
                        is_loading_any = False
                        first_product = None
                        for cargo in v.cargoes:
                            if not cargo:
                                continue
                            if first_product is None:
                                first_product = getattr(cargo, 'product', None)
                            vol = getattr(cargo, 'volume', None)
                            if isinstance(vol, (int, float)):
                                total_volume += float(vol)
                            is_loading_any = is_loading_any or bool(getattr(cargo, 'is_loading', False))
                        if first_product is not None:
                            a['product'] = a.get('product') or first_product
                            a['cargo_product'] = a.get('cargo_product') or first_product
                        if a.get('volume') in (None, 0):
                            a['volume'] = total_volume if total_volume > 0 else None
                            a['cargo_volume'] = a['volume']
                        a['is_loading'] = a.get('is_loading') if a.get('is_loading') is not None else is_loading_any
                        if 'operation' not in a:
                            a['operation'] = 'Loading' if is_loading_any else 'Unloading'
                except Exception:
                    pass

                # Enrich from DB cargoes if still missing
                try:
                    if (not a.get('product')) or (a.get('volume') in (None, 0)):
                        db_vessel_id = a.get('vessel_id')
                        if db_vessel_id:
                            try:
                                cargoes = db.get_cargoes_by_vessel(int(db_vessel_id))
                            except Exception:
                                cargoes = []
                            if cargoes:
                                total_volume = sum((c.get('volume') or 0) for c in cargoes)
                                first_product = cargoes[0].get('product')
                                is_loading_any = any(bool(c.get('is_loading')) for c in cargoes)
                                a['product'] = first_product
                                a['cargo_product'] = first_product
                                a['volume'] = total_volume if total_volume > 0 else None
                                a['cargo_volume'] = a['volume']
                                a['is_loading'] = is_loading_any
                                a['operation'] = 'Loading' if is_loading_any else 'Unloading'
                except Exception:
                    pass

                if not v or not j:
                    continue
                try:
                    features = ml_service.extract_features(v, j)
                    req = PredictionRequest(
                        vessel_id=v.id,
                        features=features,
                        jetty_id=j.id,
                        override_jetty_selection=True
                    )
                    # Validate first; attach issues for UI hints
                    try:
                        validator = MLFeatureValidator()
                        report = validator.validate_critical_features(features)
                        if not report.get('is_valid', True):
                            a['ml_validation_issues'] = list(report.get('issues', []))
                    except Exception:
                        # Best-effort validation: do not block enrichment
                        pass
                    resp = ml_service.predict_for_vessel(req, terminal)
                    if resp.success and resp.predictions:
                        def td_to_minutes(td):
                            try:
                                return int(td.total_seconds() // 60)
                            except Exception:
                                return None
                        preds = resp.predictions
                        a['ml_predicted_prepump_minutes'] = td_to_minutes(preds.pre_pump_time)
                        a['ml_predicted_pump_minutes'] = td_to_minutes(preds.pump_time)
                        a['ml_predicted_postpump_minutes'] = td_to_minutes(preds.post_pump_time)
                        a['ml_predicted_terminal_minutes'] = td_to_minutes(preds.terminal_time)
                        a['ml_average_confidence'] = float(
                            (preds.pre_pump_confidence + preds.pump_confidence + preds.post_pump_confidence + preds.terminal_confidence) / 4.0
                        ) if all(x is not None for x in [preds.pre_pump_confidence, preds.pump_confidence, preds.post_pump_confidence, preds.terminal_confidence]) else None
                        
                        # Log ML predictions for analytics
                        try:
                            predictions_dict = {
                                'ml_predicted_prepump_minutes': a['ml_predicted_prepump_minutes'],
                                'ml_predicted_pump_minutes': a['ml_predicted_pump_minutes'],
                                'ml_predicted_postpump_minutes': a['ml_predicted_postpump_minutes'],
                                'ml_predicted_terminal_minutes': a['ml_predicted_terminal_minutes']
                            }
                            confidences_dict = {
                                'prepump_confidence': preds.pre_pump_confidence,
                                'pump_confidence': preds.pump_confidence,
                                'postpump_confidence': preds.post_pump_confidence,
                                'terminal_confidence': preds.terminal_confidence
                            }
                            db.log_ml_predictions_for_assignment(
                                assignment_id=a['id'],
                                vessel_id=v.id,
                                vessel_name=v.name,
                                predictions=predictions_dict,
                                confidences=confidences_dict
                            )
                        except Exception as log_error:
                            logger.warning(f"Failed to log ML predictions for assignment {a['id']}: {log_error}")
                except Exception:
                    continue
                finally:
                    # Fallbacks to avoid empty UI fields
                    if 'ml_predicted_terminal_minutes' not in a or a.get('ml_predicted_terminal_minutes') is None:
                        try:
                            from datetime import datetime as _dt
                            st = _dt.fromisoformat(a.get('start_time')) if a.get('start_time') else None
                            et = _dt.fromisoformat(a.get('end_time')) if a.get('end_time') else None
                            if st and et:
                                a['ml_predicted_terminal_minutes'] = int((et - st).total_seconds() // 60)
                        except Exception:
                            pass
        except Exception:
            # Best-effort enrichment; ignore failures
            pass

        # Backward-compat mapping for UI keys and comprehensive fallbacks
        for a in assignments:
            # Map database fields to UI fields
            if 'product' not in a and a.get('cargo_product'):
                a['product'] = a['cargo_product']
            if 'volume' not in a and a.get('cargo_volume') is not None:
                a['volume'] = a['cargo_volume']
            
            # Add comprehensive fallbacks for missing enrichment data
            if 'product' not in a or not a['product']:
                a['product'] = a.get('cargo_product') or 'Unknown Product'
            
            if 'volume' not in a or not a['volume']:
                a['volume'] = a.get('cargo_volume') or 0
                
            if 'cargo_product' not in a or not a['cargo_product']:
                a['cargo_product'] = a.get('product') or 'Unknown Product'
                
            if 'cargo_volume' not in a or not a['cargo_volume']:
                a['cargo_volume'] = a.get('volume') or 0
            
            # Ensure operation field exists
            if 'operation' not in a:
                # Try to infer from is_loading flag or use generic default
                is_loading = a.get('is_loading')
                if is_loading is True:
                    a['operation'] = 'Loading'
                elif is_loading is False:
                    a['operation'] = 'Unloading'
                else:
                    # Generic fallback based on vessel type or use Loading as default
                    vessel_type = a.get('vessel_type', '').upper()
                    if 'BARGE' in vessel_type:
                        a['operation'] = 'Loading'  # Barges typically load
                    else:
                        a['operation'] = 'Loading'  # Default to Loading
            
            # Ensure is_loading field exists
            if 'is_loading' not in a or a['is_loading'] is None:
                # Infer from operation if available
                operation = a.get('operation', '').lower()
                if 'load' in operation:
                    a['is_loading'] = True
                elif 'unload' in operation:
                    a['is_loading'] = False
                else:
                    a['is_loading'] = True  # Default to loading
            
            # Log assignments that required fallbacks
            vessel_id = a.get('vessel_id')
            if vessel_id == 'vessel_id' or not vessel_id:
                logger.info(f"Assignment {a.get('id')} used fallback values due to corrupted vessel_id: product={a['product']}, volume={a['volume']}, operation={a['operation']}")
        return assignments
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load assignments: {str(e)}")


@app.get("/api/schedule/assignments/{assignment_id}")
async def get_assignment_api(assignment_id: int, state: Dict = Depends(get_data)):
    """Proxy to DB-backed single assignment fetch (compat)."""
    try:
        terminal_id = db.get_active_terminal_id()
        assignments = db.get_assignments(terminal_id)
        assignment = next((a for a in assignments if str(a.get('id')) == str(assignment_id)), None)
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")
        # Add compatibility fields expected by some templates
        enriched = dict(assignment)
        if 'jetty_id' not in enriched and 'jetty_name' in enriched:
            enriched['jetty_id'] = enriched['jetty_name']
        # Enrich with ML prediction for this assignment
        try:
            from src.ml.prediction_service import MLPredictionService
            from src.ml.models import PredictionRequest
            from src.ml.feature_validator import MLFeatureValidator
            ml_service = state.get("ml_service") or MLPredictionService()
            terminal = state.get('terminal')
            v = next((vx for vx in state.get('vessels', []) if getattr(vx, 'id') == assignment.get('vessel_id')), None)
            # Lookup jetty by name or id
            j = None
            if terminal and hasattr(terminal, 'jetties'):
                for jj in terminal.jetties:
                    if getattr(jj, 'name', None) == assignment.get('jetty_name') or str(getattr(jj, 'id', '')) == str(assignment.get('jetty_name')):
                        j = jj
                        break
            if v and j:
                features = ml_service.extract_features(v, j)
                req = PredictionRequest(vessel_id=v.id, features=features, jetty_id=j.id, override_jetty_selection=True)
                try:
                    validator = MLFeatureValidator()
                    report = validator.validate_critical_features(features)
                    if not report.get('is_valid', True):
                        enriched['ml_validation_issues'] = list(report.get('issues', []))
                except Exception:
                    # Best-effort validation: do not block enrichment
                    pass
                resp = ml_service.predict_for_vessel(req, terminal)
                if resp.success and resp.predictions:
                    def td_to_minutes(td):
                        try:
                            return int(td.total_seconds() // 60)
                        except Exception:
                            return None
                    preds = resp.predictions
                    enriched['ml_predicted_prepump_minutes'] = td_to_minutes(preds.pre_pump_time)
                    enriched['ml_predicted_pump_minutes'] = td_to_minutes(preds.pump_time)
                    enriched['ml_predicted_postpump_minutes'] = td_to_minutes(preds.post_pump_time)
                    enriched['ml_predicted_terminal_minutes'] = td_to_minutes(preds.terminal_time)
                    
                    # Log ML predictions for analytics
                    try:
                        predictions_dict = {
                            'ml_predicted_prepump_minutes': enriched['ml_predicted_prepump_minutes'],
                            'ml_predicted_pump_minutes': enriched['ml_predicted_pump_minutes'],
                            'ml_predicted_postpump_minutes': enriched['ml_predicted_postpump_minutes'],
                            'ml_predicted_terminal_minutes': enriched['ml_predicted_terminal_minutes']
                        }
                        confidences_dict = {
                            'prepump_confidence': preds.pre_pump_confidence,
                            'pump_confidence': preds.pump_confidence,
                            'postpump_confidence': preds.post_pump_confidence,
                            'terminal_confidence': preds.terminal_confidence
                        }
                        db.log_ml_predictions_for_assignment(
                            assignment_id=assignment_id,
                            vessel_id=v.id,
                            vessel_name=v.name,
                            predictions=predictions_dict,
                            confidences=confidences_dict
                        )
                    except Exception as log_error:
                        logger.warning(f"Failed to log ML predictions for assignment {assignment_id}: {log_error}")
        except Exception:
            pass
        finally:
            # Fallback if ML enrichment failed
            if 'ml_predicted_terminal_minutes' not in enriched or enriched.get('ml_predicted_terminal_minutes') is None:
                try:
                    from datetime import datetime as _dt
                    st = _dt.fromisoformat(enriched.get('start_time')) if enriched.get('start_time') else None
                    et = _dt.fromisoformat(enriched.get('end_time')) if enriched.get('end_time') else None
                    if st and et:
                        enriched['ml_predicted_terminal_minutes'] = int((et - st).total_seconds() // 60)
                except Exception:
                    pass
        return enriched
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load assignment: {str(e)}")


@app.patch("/api/schedule/assignments/{assignment_id}")
async def patch_assignment_api(assignment_id: int, body: Dict[str, Any]):
    """Update assignment start/end with a required reason; records change log."""
    try:
        terminal_id = db.get_active_terminal_id()
        # Load current assignment
        assignments = db.get_assignments(terminal_id)
        assignment = next((a for a in assignments if int(a.get('id')) == int(assignment_id)), None)
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        new_start = body.get('start_time')
        new_end = body.get('end_time')
        new_jetty = body.get('jetty_name') or body.get('jetty_id')  # accept either, DB stores name compat
        override_ml_terminal = body.get('ml_terminal_minutes')
        reason = (body.get('reason') or '').strip()
        changed_by = (body.get('changed_by') or '').strip() or 'user'
        # Allow jetty-only changes without requiring start/end (keep existing times)
        if (new_start is None or new_end is None) and new_jetty is None:
            raise HTTPException(status_code=400, detail="start_time and end_time are required unless only changing jetty")
        if not reason:
            raise HTTPException(status_code=400, detail="reason is required")

        # Update assignment
        payload = {
            'vessel_id': assignment['vessel_id'],
            'vessel_name': assignment['vessel_name'],
            'vessel_type': assignment['vessel_type'],
            'jetty_name': new_jetty if new_jetty is not None else assignment['jetty_name'],
            'start_time': new_start if new_start is not None else assignment.get('start_time'),
            'end_time': new_end if new_end is not None else assignment.get('end_time'),
            'status': assignment.get('status', 'SCHEDULED')
        }
        updated = db.update_assignment(assignment_id, payload)
        if not updated:
            raise HTTPException(status_code=500, detail="Failed to update assignment")

        # Log change
        # If jetty changed, include that context in reason message for audit readability
        final_reason = reason
        if new_jetty is not None and str(new_jetty) != str(assignment.get('jetty_name')):
            try:
                final_reason = f"Jetty change: {assignment.get('jetty_name')} → {new_jetty}. {reason}"
            except Exception:
                final_reason = reason

        # Ensure DateTime objects for logging (DB expects datetime, not ISO strings)
        from datetime import datetime as _dt
        from ..utils import datetime_utils as dtutils
        def _parse_dt(val):
            # Delegate to shared util to normalize to UTC microseconds
            try:
                return dtutils.normalize_iso_to_utc_microseconds(val)
            except Exception:
                return None

        old_start_dt = _parse_dt(assignment.get('start_time')) or assignment.get('start_time')
        old_end_dt = _parse_dt(assignment.get('end_time')) or assignment.get('end_time')
        new_start_dt = _parse_dt(new_start) or new_start
        new_end_dt = _parse_dt(new_end) or new_end

        # PostgreSQL-only: Always use datetime objects
        db.log_assignment_change(
            assignment_id=assignment_id,
            old_start_time=old_start_dt,
            old_end_time=old_end_dt,
            new_start_time=new_start_dt,
            new_end_time=new_end_dt,
            reason=final_reason,
            vessel_id=assignment.get('vessel_id'),
            vessel_name=assignment.get('vessel_name'),
            jetty_name=new_jetty if new_jetty is not None else assignment.get('jetty_name'),
            changed_by=changed_by,
            terminal_id=terminal_id
        )
        
        # Log detailed change analysis for analytics (PostgreSQL-only)
        try:
            # Log start time change if different
            if new_start_dt and old_start_dt and new_start_dt != old_start_dt:
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='start_time',
                    reason_text=final_reason,
                    original_value=old_start_dt.isoformat() if hasattr(old_start_dt, 'isoformat') else str(old_start_dt),
                    new_value=new_start_dt.isoformat() if hasattr(new_start_dt, 'isoformat') else str(new_start_dt),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id
                )
            
            # Log end time change if different  
            if new_end_dt and old_end_dt and new_end_dt != old_end_dt:
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='end_time',
                    reason_text=final_reason,
                    original_value=old_end_dt.isoformat() if hasattr(old_end_dt, 'isoformat') else str(old_end_dt),
                    new_value=new_end_dt.isoformat() if hasattr(new_end_dt, 'isoformat') else str(new_end_dt),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id
                )
            
            # Log jetty change if different
            if new_jetty is not None and str(new_jetty) != str(assignment.get('jetty_name')):
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='jetty',
                    reason_text=final_reason,
                    original_value=str(assignment.get('jetty_name')),
                    new_value=str(new_jetty),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id
                )
        except Exception as analytics_error:
            logger.warning(f"Failed to log change analysis for assignment {assignment_id}: {analytics_error}")


        # PostgreSQL-only: Log categorized change analysis
        try:
            def _to_iso(v):
                try:
                    if isinstance(v, _dt):
                        return v.isoformat()
                    if isinstance(v, str):
                        return v
                except Exception:
                    pass
                return None

            # Detect which fields actually changed
            start_changed = new_start is not None and old_start_dt is not None and _to_iso(old_start_dt) != _to_iso(new_start_dt)
            end_changed = new_end is not None and old_end_dt is not None and _to_iso(old_end_dt) != _to_iso(new_end_dt)
            jetty_changed = new_jetty is not None and str(new_jetty) != str(assignment.get('jetty_name'))

            if start_changed:
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='start_time',
                    reason_text=final_reason,
                    original_value=_to_iso(old_start_dt),
                    new_value=_to_iso(new_start_dt),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id,
                )
            if end_changed:
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='end_time',
                    reason_text=final_reason,
                    original_value=_to_iso(old_end_dt),
                    new_value=_to_iso(new_end_dt),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id,
                )
            if jetty_changed:
                db.log_change_analysis(
                    assignment_id=assignment_id,
                    change_type='jetty',
                    reason_text=final_reason,
                    original_value=str(assignment.get('jetty_name') or ''),
                    new_value=str(new_jetty),
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    changed_by=changed_by,
                    terminal_id=terminal_id,
                )
        except Exception:
            # Do not fail the main update if analytics logging fails
            pass

        # Attach a non-persistent hint back to the client if ML override was provided
        response = {"success": True}
        if override_ml_terminal is not None:
            response["ml_terminal_minutes"] = override_ml_terminal
        return response
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update assignment: {str(e)}")


@app.get("/api/schedule/changes")
async def get_schedule_changes(limit: int = 50):
    """Return recent assignment change log entries."""
    try:
        changes = db.get_assignment_changes(limit=limit)
        return changes
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load schedule changes: {str(e)}")


@app.post("/api/schedule/reset-to-nominations")
async def reset_to_nominations_api(state: Dict = Depends(get_data)):
    """Move all planned assignments back to nominations for re-optimization."""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=400, detail="No active terminal configured")
        
        # Get current assignments before clearing
        current_assignments = db.get_assignments(terminal_id)
        
        # Clear all assignments
        db.clear_assignments(terminal_id)
        
        # Update vessel statuses back to unscheduled
        reset_count = 0
        for assignment in current_assignments:
            vessel_id = assignment.get('vessel_id')
            
            # Find vessel in memory and reset status
            vessel = next((v for v in state.get("vessels", []) if getattr(v, 'id') == vessel_id), None)
            if vessel:
                old_status = vessel.status
                vessel.status = "APPROACHING"  # Back to unscheduled status
                reset_count += 1
                logger.debug(f"Reset vessel {vessel_id} status from {old_status} to {vessel.status} (back to nomination)")
        
        # Log the reset operation
        try:
            for assignment in current_assignments:
                db.log_assignment_change(
                    assignment_id=assignment.get('id'),
                    old_start_time=assignment.get('start_time'),
                    old_end_time=assignment.get('end_time'),
                    new_start_time=None,  # Back to nomination, no assignment
                    new_end_time=None,    # Back to nomination, no assignment
                    reason="Planned assignments reset to nominations for re-optimization",
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    jetty_name=assignment.get('jetty_name'),
                    changed_by="User - Manual Reset",
                    terminal_id=terminal_id
                )
        except Exception as log_err:
            logger.warning(f"Failed to log assignment reset: {log_err}")
        
        logger.info(f"Reset {len(current_assignments)} assignments to nominations, updated {reset_count} vessel statuses")
        
        return {
            "success": True,
            "message": f"Reset {len(current_assignments)} assignments to nominations",
            "assignments_reset": len(current_assignments),
            "vessels_updated": reset_count
        }
        
    except Exception as e:
        logger.error(f"Error resetting assignments to nominations: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset assignments: {str(e)}")


@app.get("/.well-known/appspecific/com.chrome.devtools.json")
async def chrome_devtools_config():
    """Suppress Chrome DevTools 404 requests"""
    return {}


@app.post("/api/optimize")
async def optimize_schedule_api(
    params: OptimizationParameters,
    background_tasks: BackgroundTasks,
    state: Dict = Depends(get_data)
):
    """Run optimization to create a new schedule API endpoint"""
    if state["optimization_in_progress"]:
        return OptimizationResponse(
            success=False,
            message="Optimization already in progress"
        )

    # Set optimization in progress
    state["optimization_in_progress"] = True

    # Run optimization in background (use keyword args for forward-compatibility)
    background_tasks.add_task(
        run_optimization,
        state=state,
        horizon_days=params.horizon_days,
        time_granularity_hours=params.time_granularity_hours,
        weight_throughput=params.weight_throughput,
        weight_demurrage=params.weight_demurrage,
        weight_priority=params.weight_priority,
        weight_weather=params.weight_weather,
        force_assign_all=params.force_assign_all,
        include_mock_assignments=params.include_mock_assignments,
        approach_time_hours=params.approach_time_hours,
        free_wait_buffer_hours=params.free_wait_buffer_hours,
        preserve_locked=params.preserve_locked,
        vessel_filter=params.vessel_filter,
        time_window_start=params.time_window_start,
        time_window_end=params.time_window_end,
        fill_unassigned=params.fill_unassigned
    )

    return OptimizationResponse(
        success=True,
        message="Optimization started in the background"
    )


# Selective optimization request model
class SelectiveOptimizationRequest(BaseModel):
    vessel_ids: List[str]
    parameters: Optional[OptimizationParameters] = None


@app.post("/api/optimize/selective")
async def optimize_selective_api(
    req: SelectiveOptimizationRequest,
    background_tasks: BackgroundTasks,
    state: Dict = Depends(get_data)
):
    """Run optimization only for the specified vessels, preserving locked assignments by default."""
    params = req.parameters or OptimizationParameters()
    if state["optimization_in_progress"]:
        return OptimizationResponse(success=False, message="Optimization already in progress")
    state["optimization_in_progress"] = True
    background_tasks.add_task(
        run_optimization,
        state=state,
        horizon_days=params.horizon_days,
        time_granularity_hours=params.time_granularity_hours,
        weight_throughput=params.weight_throughput,
        weight_demurrage=params.weight_demurrage,
        weight_priority=params.weight_priority,
        weight_weather=params.weight_weather,
        force_assign_all=params.force_assign_all,
        include_mock_assignments=params.include_mock_assignments,
        approach_time_hours=params.approach_time_hours,
        free_wait_buffer_hours=params.free_wait_buffer_hours,
        preserve_locked=params.preserve_locked,
        vessel_filter=req.vessel_ids,
        time_window_start=params.time_window_start,
        time_window_end=params.time_window_end,
        fill_unassigned=params.fill_unassigned
    )
    return OptimizationResponse(success=True, message="Selective optimization started in the background")


class TimeWindowOptimizationRequest(BaseModel):
    start: datetime
    end: datetime
    parameters: Optional[OptimizationParameters] = None


@app.post("/api/optimize/time-window")
async def optimize_time_window_api(
    req: TimeWindowOptimizationRequest,
    background_tasks: BackgroundTasks,
    state: Dict = Depends(get_data)
):
    """Run optimization focusing on a time window and preserving locked assignments by default."""
    params = req.parameters or OptimizationParameters()
    if state["optimization_in_progress"]:
        return OptimizationResponse(success=False, message="Optimization already in progress")
    state["optimization_in_progress"] = True
    background_tasks.add_task(
        run_optimization,
        state=state,
        horizon_days=params.horizon_days,
        time_granularity_hours=params.time_granularity_hours,
        weight_throughput=params.weight_throughput,
        weight_demurrage=params.weight_demurrage,
        weight_priority=params.weight_priority,
        weight_weather=params.weight_weather,
        force_assign_all=params.force_assign_all,
        include_mock_assignments=params.include_mock_assignments,
        approach_time_hours=params.approach_time_hours,
        free_wait_buffer_hours=params.free_wait_buffer_hours,
        preserve_locked=params.preserve_locked,
        vessel_filter=params.vessel_filter,
        time_window_start=req.start,
        time_window_end=req.end,
        fill_unassigned=params.fill_unassigned
    )
    return OptimizationResponse(success=True, message="Time-window optimization started in the background")


@app.post("/api/optimize/preview", response_model=ImpactPreviewResponse)
async def preview_optimization_impact(params: OptimizationParameters, state: Dict = Depends(get_data)):
    """Return a quick impact summary without running the solver."""
    try:
        terminal_id = db.get_active_terminal_id()
        assignments = db.get_assignments(terminal_id)

        # Count locked vs unlocked
        locked = 0
        unlocked = 0
        for a in assignments:
            ls = (a.get('lock_status') or 'UNLOCKED').upper()
            if ls != 'UNLOCKED':
                locked += 1
            else:
                unlocked += 1

        # Determine vessels considered (filtering + statuses)
        relevant_statuses = {"EN_ROUTE", "APPROACHING", "ARRIVED", "WAITING"}
        vessels = [v for v in state.get("vessels", []) if str(getattr(v, 'status', '')).upper() in relevant_statuses]
        if params.vessel_filter:
            vf = set(str(x) for x in params.vessel_filter)
            vessels = [v for v in vessels if str(getattr(v, 'id', '')) in vf]

        # Time window echo
        tw = None
        if params.time_window_start or params.time_window_end:
            tw = {
                "start": (params.time_window_start.isoformat() if params.time_window_start else None),
                "end": (params.time_window_end.isoformat() if params.time_window_end else None)
            }

        # Very rough estimate placeholders
        est = {
            "throughput_percent": 10.0 if not params.force_assign_all else 5.0,
            "avg_wait_hours_delta": -1.5,
        }

        msg = "Preview computed"
        return ImpactPreviewResponse(
            success=True,
            message=msg,
            locked_preserved=locked if params.preserve_locked else 0,
            unlocked_candidates=unlocked,
            vessels_considered=len(vessels),
            time_window=tw,
            estimated_improvement=est
        )
    except Exception as e:
        return ImpactPreviewResponse(success=False, message=f"Failed to build preview: {e}")

    return OptimizationResponse(
        success=True,
        message="Optimization started in the background"
    )


@app.get("/api/optimize/status")
async def get_optimization_status_api(state: Dict = Depends(get_data)):
    """Get the status of the optimization process API endpoint"""
    return {
        "in_progress": state["optimization_in_progress"],
        "last_optimization_time": state["last_optimization_time"].isoformat() if state["last_optimization_time"] else None,
        "result": state.get("last_optimization_result")
    }


# Models for creating new resources
class NewVesselRequest(BaseModel):
    """Request model for creating a new vessel"""
    name: str
    vessel_type: str  # TANKER, BARGE, etc.
    length: float
    beam: float
    draft: float
    deadweight: float
    eta: Optional[datetime] = None
    status: str = "EN_ROUTE"  # Default status
    customer: Optional[str] = None
    priority: int = 1
    cargoes: List[Dict[str, Any]] = []


class NewAssignmentRequest(BaseModel):
    """Request model for creating a new assignment"""
    jetty_id: str
    vessel_id: str
    start_time: datetime
    end_time: datetime
    status: str = "PENDING_APPROVAL"
    surveyor_ids: List[str] = []
    pump_ids: List[str] = []
    tank_ids: List[str] = []
    notes: Optional[str] = None
    cargo_product: Optional[str] = None
    cargo_volume: Optional[float] = None


@app.post("/api/vessels")
async def create_vessel_api(
    new_vessel: NewVesselRequest,
    state: Dict = Depends(get_data)
):
    """Create a new vessel API endpoint"""
    try:
        # Convert string vessel type to enum
        vessel_type = VesselType(new_vessel.vessel_type)

        # Just normalize the status string instead of converting to enum
        vessel_status = normalize_status(new_vessel.status)
        if not is_valid_vessel_status(vessel_status):
            raise HTTPException(status_code=400, detail=f"Invalid vessel status: {new_vessel.status}")

        # Create cargoes if provided
        cargoes = []
        for cargo_data in new_vessel.cargoes:
            cargo = Cargo(
                product=cargo_data.get("product", "Unknown"),
                volume=cargo_data.get("volume", 0.0),
                is_loading=cargo_data.get("is_loading", False),
                tanks=cargo_data.get("tanks", []),
                surveyor_required=cargo_data.get("surveyor_required", True)
            )
            cargoes.append(cargo)

        # Generate a unique ID
        vessel_id = f"V{len(state['vessels']) + 1:03d}"

        # Create vessel object
        if vessel_type == VesselType.BARGE:
            vessel = Barge(
                id=vessel_id,
                name=new_vessel.name,
                length=new_vessel.length,
                beam=new_vessel.beam,
                draft=new_vessel.draft,
                deadweight=new_vessel.deadweight,
                cargoes=cargoes,
                status=vessel_status,  # Use normalized string status
                eta=new_vessel.eta,
                customer=new_vessel.customer,
                priority=new_vessel.priority
            )
        else:
            vessel = Vessel(
                id=vessel_id,
                name=new_vessel.name,
                vessel_type=vessel_type,
                length=new_vessel.length,
                beam=new_vessel.beam,
                draft=new_vessel.draft,
                deadweight=new_vessel.deadweight,
                cargoes=cargoes,
                status=vessel_status,  # Use normalized string status
                eta=new_vessel.eta,
                customer=new_vessel.customer,
                priority=new_vessel.priority
            )

        # Add to vessels list
        state["vessels"].append(vessel)

        return {
            "success": True,
            "id": vessel.id,
            "name": vessel.name,
            "message": f"Vessel {vessel.name} created successfully"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid vessel data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating vessel: {str(e)}")


@app.post("/api/schedule/assignments")
async def create_assignment_api(new_assignment: NewAssignmentRequest):
    """Create assignment via DB to ensure persistence (compat)."""
    try:
        normalized_status = normalize_status(new_assignment.status) if new_assignment.status else "PENDING_APPROVAL"
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=400, detail="No active terminal configured")

        assignment_dict = {
            'terminal_id': terminal_id,
            'vessel_id': new_assignment.vessel_id,
            'vessel_name': f"Vessel {new_assignment.vessel_id}",
            'vessel_type': "TANKER",
            'jetty_name': new_assignment.jetty_id,
            'cargo_product': new_assignment.cargo_product,
            'cargo_volume': new_assignment.cargo_volume,
            'start_time': new_assignment.start_time.isoformat(),
            'end_time': new_assignment.end_time.isoformat(),
            'status': normalized_status
        }
        db_assignment_id = db.add_assignment(assignment_dict)
        if not db_assignment_id:
            raise HTTPException(status_code=500, detail="Failed to create assignment in database")
        
        # Log assignment creation for analytics
        try:
            db.log_assignment_change(
                assignment_id=db_assignment_id,
                old_start_time=None,
                old_end_time=None,
                new_start_time=new_assignment.start_time.isoformat(),
                new_end_time=new_assignment.end_time.isoformat(),
                reason="Manual assignment created by user",
                vessel_id=new_assignment.vessel_id,
                vessel_name=f"Vessel {new_assignment.vessel_id}",
                jetty_name=new_assignment.jetty_id,
                changed_by="user",
                terminal_id=db.get_active_terminal_id()
            )
            
            # Log detailed change analysis
            db.log_change_analysis(
                assignment_id=db_assignment_id,
                change_type='assignment_created',
                reason_text="Manual assignment created by user",
                original_value=None,
                new_value=f"Jetty: {new_assignment.jetty_id}, {new_assignment.start_time} - {new_assignment.end_time}",
                vessel_id=new_assignment.vessel_id,
                vessel_name=f"Vessel {new_assignment.vessel_id}",
                changed_by="user",
                terminal_id=db.get_active_terminal_id()
            )
        except Exception as log_e:
            logger.warning(f"Failed to log assignment creation: {log_e}")
        
        return {
            "id": db_assignment_id,
            "jetty_id": new_assignment.jetty_id,
            "vessel_id": new_assignment.vessel_id,
            "cargo_product": new_assignment.cargo_product,
            "cargo_volume": new_assignment.cargo_volume,
            "start_time": new_assignment.start_time.isoformat(),
            "end_time": new_assignment.end_time.isoformat(),
            "status": normalized_status
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating assignment: {str(e)}")


@app.put("/api/schedule/assignments/{assignment_id}")
async def update_assignment_api(assignment_id: int, update: AssignmentUpdate):
    """Update assignment persistently (compat)."""
    terminal_id = db.get_active_terminal_id()
    if not terminal_id:
        raise HTTPException(status_code=404, detail="No active terminal")
    assignments = db.get_assignments(terminal_id)
    current = next((a for a in assignments if int(a.get('id')) == int(assignment_id)), None)
    if not current:
        raise HTTPException(status_code=404, detail="Assignment not found")
    update_dict = {
        'vessel_id': update.vessel_id if update.vessel_id is not None else current.get('vessel_id'),
        'vessel_name': current.get('vessel_name'),
        'vessel_type': current.get('vessel_type'),
        'jetty_name': update.jetty_id if update.jetty_id is not None else current.get('jetty_name'),
        'start_time': update.start_time.isoformat() if update.start_time is not None else current.get('start_time'),
        'end_time': update.end_time.isoformat() if update.end_time is not None else current.get('end_time'),
        'status': normalize_status(update.status) if update.status is not None else current.get('status')
    }
    success = db.update_assignment(int(assignment_id), update_dict)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to update assignment in database")
    return {
        "id": int(assignment_id),
        "jetty_id": update_dict['jetty_name'],
        "vessel_id": update_dict['vessel_id'],
        "start_time": update_dict['start_time'],
        "end_time": update_dict['end_time'],
        "status": update_dict['status']
    }


@app.post("/api/schedule/assignments/{assignment_id}/unschedule")
async def unschedule_assignment_api(
    assignment_id: int,
    request: Request,
    state: Dict = Depends(get_data)
):
    """Unschedule an assignment and return the vessel to unscheduled status.
    
    This cancels the assignment and makes the vessel available for optimization again.
    """
    try:
        # Parse request body for reason
        request_body = await request.json() if request.headers.get("content-type") == "application/json" else {}
        reason = request_body.get('reason', 'Assignment unscheduled')
        
        terminal_id = db.get_active_terminal_id()
        
        # Get the assignment details first
        assignment = db.get_assignment(assignment_id)
        if not assignment:
            raise HTTPException(status_code=404, detail=f"Assignment {assignment_id} not found")
        
        # Debug logging for PostgreSQL migration issues
        logger.info(f"Retrieved assignment {assignment_id}: {assignment}")
        
        # Check if assignment can be unscheduled (not completed or in progress)
        current_status = assignment.get('status', '').upper()
        if current_status in ['COMPLETED', 'IN_PROGRESS', 'ACTIVE']:
            raise HTTPException(
                status_code=400, 
                detail=f"Cannot unschedule assignment with status {current_status}. Only SCHEDULED assignments can be unscheduled."
            )
        
        vessel_id = assignment.get('vessel_id')
        vessel_name = assignment.get('vessel_name')
        
        # Additional debugging for vessel_id issues
        logger.info(f"Extracted vessel_id: '{vessel_id}' (type: {type(vessel_id)})")
        logger.info(f"Extracted vessel_name: '{vessel_name}' (type: {type(vessel_name)})")
        
        # Validate vessel_id - handle common database migration issues
        if not vessel_id or vessel_id == 'vessel_id' or (isinstance(vessel_id, str) and vessel_id.strip() == ''):
            logger.error(f"Invalid vessel_id detected: '{vessel_id}' for assignment {assignment_id}")
            logger.error(f"Full assignment data: {assignment}")
            
            # Try to get a meaningful vessel name or ID from the assignment
            fallback_vessel_id = assignment.get('vessel_name', f'assignment_{assignment_id}')
            if fallback_vessel_id and fallback_vessel_id != 'vessel_id' and fallback_vessel_id.strip():
                logger.info(f"Using fallback vessel_id: '{fallback_vessel_id}' for assignment {assignment_id}")
                vessel_id = fallback_vessel_id
                vessel_name = fallback_vessel_id
                
                # Update the assignment with the corrected vessel_id to prevent future issues
                try:
                    db.update_assignment(assignment_id, {'vessel_id': vessel_id})
                    logger.info(f"Updated assignment {assignment_id} with corrected vessel_id: '{vessel_id}'")
                except Exception as update_error:
                    logger.warning(f"Failed to update assignment {assignment_id} with corrected vessel_id: {update_error}")
            else:
                # As a last resort, create a meaningful vessel_id from the assignment
                jetty_name = assignment.get('jetty_name', '').strip()
                if jetty_name:
                    vessel_id = f"vessel_at_{jetty_name.replace(' ', '_').lower()}"
                    vessel_name = vessel_id
                    logger.info(f"Generated vessel_id from jetty: '{vessel_id}' for assignment {assignment_id}")
                    
                    # Update the assignment
                    try:
                        db.update_assignment(assignment_id, {'vessel_id': vessel_id, 'vessel_name': vessel_name})
                        logger.info(f"Updated assignment {assignment_id} with generated vessel_id: '{vessel_id}'")
                    except Exception as update_error:
                        logger.warning(f"Failed to update assignment {assignment_id} with generated vessel_id: {update_error}")
                else:
                    # Final fallback
                    vessel_id = f"vessel_{assignment_id}"
                    vessel_name = vessel_id
                    logger.info(f"Using final fallback vessel_id: '{vessel_id}' for assignment {assignment_id}")
                    
                    try:
                        db.update_assignment(assignment_id, {'vessel_id': vessel_id, 'vessel_name': vessel_name})
                        logger.info(f"Updated assignment {assignment_id} with fallback vessel_id: '{vessel_id}'")
                    except Exception as update_error:
                        logger.warning(f"Failed to update assignment {assignment_id} with fallback vessel_id: {update_error}")
                        # Don't fail the unschedule operation just because we can't fix the vessel_id
                        pass
        
        # Cancel the assignment
        logger.info(f"STEP 1: About to cancel assignment {assignment_id}")
        updated = db.update_assignment(assignment_id, {'status': 'CANCELLED'})
        if not updated:
            raise HTTPException(status_code=500, detail="Failed to cancel assignment")
        logger.info(f"STEP 1 COMPLETE: Assignment {assignment_id} cancelled successfully")
        
        # Log the unschedule action
        logger.info(f"STEP 2: About to log assignment change for {assignment_id}")
        try:
            db.log_assignment_change(
                assignment_id=assignment_id,
                old_start_time=assignment.get('start_time'),
                old_end_time=assignment.get('end_time'),
                new_start_time=None,
                new_end_time=None,
                reason=reason,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                jetty_name=assignment.get('jetty_name'),
                changed_by='system',
                terminal_id=terminal_id
            )
        except Exception as log_error:
            logger.warning(f"Failed to log unschedule action: {log_error}")
        logger.info(f"STEP 2 COMPLETE: Assignment change logged for {assignment_id}")
        
        # DATABASE-FIRST: Handle vessel status updates through database only
        logger.info(f"STEP 3: DATABASE-FIRST vessel handling for {vessel_id}")
        vessel_updated = False
        
        try:
            # Use VesselService for database-first vessel operations
            from src.services.vessel_service import VesselService
            vessel_service = VesselService(db)
            
            terminal_id = db.get_active_terminal_id()
            if not terminal_id:
                logger.warning("No active terminal found for vessel update")
            else:
                # Try to update vessel status in database
                try:
                    # First try as integer vessel ID (database vessels)
                    vessel_id_int = int(vessel_id)
                    vessel = db.get_vessel(vessel_id_int)
                    if vessel:
                        db.update_vessel(vessel_id_int, {'status': 'APPROACHING'})
                        vessel_updated = True
                        logger.info(f"DATABASE-FIRST: Updated database vessel {vessel_id} status to APPROACHING")
                except (ValueError, TypeError):
                    # Not a database vessel, try nomination update
                    logger.info(f"Vessel {vessel_id} is not a database vessel, checking nominations")
                    
                    # Check if this vessel_id corresponds to a nomination
                    nominations = db.get_nominations(terminal_id) or []
                    for nom in nominations:
                        nom_vessel_id = str(nom.get('runtime_vessel_id', nom.get('id', '')))
                        if nom_vessel_id == str(vessel_id):
                            # Update nomination status to make vessel available again
                            try:
                                db.update_nomination(nom.get('id'), {'status': 'ACTIVE'})
                                vessel_updated = True
                                logger.info(f"DATABASE-FIRST: Updated nomination {nom.get('id')} status to ACTIVE")
                                break
                            except Exception as nom_error:
                                logger.warning(f"Failed to update nomination status: {nom_error}")
                    
                    if not vessel_updated:
                        logger.info(f"DATABASE-FIRST: Vessel {vessel_id} not found in database or nominations")
                        
        except Exception as e:
            logger.error(f"DATABASE-FIRST: Error handling vessel {vessel_id}: {e}")
            # Don't fail the unschedule operation just because vessel update failed
            pass
        
        logger.info(f"STEP 3 COMPLETE: Vessel handling completed, vessel_updated={vessel_updated}")
        
        logger.info(f"STEP 4: Returning success response for assignment {assignment_id}")
        return {
            "success": True,
            "message": f"Assignment {assignment_id} unscheduled successfully",
            "vessel_id": vessel_id,
            "vessel_available": vessel_updated
        }
        
    except HTTPException:
        raise
    except KeyError as e:
        import traceback
        logger.error(f"KeyError unscheduling assignment {assignment_id}: {e}")
        logger.error(f"Assignment data: {assignment}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to unschedule assignment: Missing required field {str(e)}. This may be due to corrupted assignment data.")
    except Exception as e:
        import traceback
        logger.error(f"Error unscheduling assignment {assignment_id}: {e}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to unschedule assignment: {str(e)}")


@app.delete("/api/schedule/assignments/{assignment_id}")
async def delete_assignment_api(assignment_id: int):
    """Delete assignment from DB (compat)."""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Load assignment to capture old values for the change log
        assignments = db.get_assignments(terminal_id)
        assignment = next((a for a in assignments if int(a.get('id')) == int(assignment_id)), None)
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Prepare change-log values
        def _parse_dt(val):
            try:
                from src.utils import datetime_utils as dtutils
                return dtutils.normalize_iso_to_utc_microseconds(val)
            except Exception:
                return None

        old_start_dt = _parse_dt(assignment.get('start_time')) or assignment.get('start_time')
        old_end_dt = _parse_dt(assignment.get('end_time')) or assignment.get('end_time')

        try:
            is_pg = getattr(db, "_is_postgres", lambda: False)()
        except Exception:
            is_pg = False

        # Log deletion in assignment change log (don't block deletion on logging errors)
        try:
            if is_pg:
                db.log_assignment_change(
                    assignment_id=int(assignment_id),
                    old_start_time=old_start_dt,
                    old_end_time=old_end_dt,
                    new_start_time=None,
                    new_end_time=None,
                    reason="Assignment deleted",
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    jetty_name=assignment.get('jetty_name'),
                    changed_by="user",
                    terminal_id=terminal_id,
                )
            else:
                def _iso(v):
                    try:
                        from datetime import datetime as _dt
                        if isinstance(v, _dt):
                            return v.isoformat(sep=' ')
                        if isinstance(v, str):
                            return v.replace('T', ' ')
                    except Exception:
                        pass
                    return v

                db.log_assignment_change(
                    assignment_id=int(assignment_id),
                    old_start_time=_iso(old_start_dt),
                    old_end_time=_iso(old_end_dt),
                    new_start_time=None,
                    new_end_time=None,
                    reason="Assignment deleted",
                    vessel_id=assignment.get('vessel_id'),
                    vessel_name=assignment.get('vessel_name'),
                    jetty_name=assignment.get('jetty_name'),
                    changed_by="user",
                    terminal_id=terminal_id,
                )
        except Exception:
            pass

        # Perform deletion
        success = db.delete_assignment(int(assignment_id))
        if not success:
            raise HTTPException(status_code=404, detail="Assignment not found")
        return {"message": "Assignment deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete assignment: {str(e)}")


@app.delete("/api/vessels/{vessel_id}")
async def delete_vessel_api(
    vessel_id: str,
    state: Dict = Depends(get_data)
):
    """Delete a vessel by ID from persistent storage and in-memory state.

    This removes DB-backed vessels as well as nomination-created runtime vessels that
    were persisted. For safety, it will fail if the vessel is referenced by any
    assignments in the database.
    """
    try:
        # Prevent deleting vessels referenced by assignments
        terminal_id = db.get_active_terminal_id()
        assignments = db.get_assignments(terminal_id)
        if any(str(a.get('vessel_id')) == str(vessel_id) for a in assignments):
            raise HTTPException(status_code=400, detail=f"Cannot delete vessel {vessel_id} as it is used in existing assignments")

        # DATABASE-FIRST: Use VesselService for deletion
        try:
            from src.services.vessel_service import VesselService
            vessel_service = VesselService(db)
            
            terminal_id = db.get_active_terminal_id()
            if not terminal_id:
                raise HTTPException(status_code=400, detail="No active terminal found")
            
            # Delete vessel through service (handles logging and database operations)
            deleted = vessel_service.delete_vessel(vessel_id, terminal_id)
            
            if not deleted:
                raise HTTPException(status_code=404, detail=f"Vessel {vessel_id} not found")
            
            # Also remove from in-memory state for backward compatibility
            if "vessels" in state:
                initial_count = len(state["vessels"])
                state["vessels"] = [v for v in state["vessels"] if str(getattr(v, 'id', None)) != str(vessel_id)]
                removed_from_memory = len(state["vessels"]) < initial_count
                
                if removed_from_memory:
                    logger.info(f"Also removed vessel {vessel_id} from in-memory state")
            
            logger.info(f"DATABASE-FIRST: Vessel {vessel_id} deleted via VesselService")
            return {"success": True, "message": f"Vessel {vessel_id} deleted successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error using VesselService for deletion: {e}")
            # Fallback to original deletion logic
            deleted = db.delete_vessel(vessel_id)
            
            if "vessels" in state:
                initial_count = len(state["vessels"])
                state["vessels"] = [v for v in state["vessels"] if str(getattr(v, 'id', None)) != str(vessel_id)]
                removed_from_memory = len(state["vessels"]) < initial_count
                
                if removed_from_memory:
                    logger.info(f"Removed vessel {vessel_id} from in-memory state")
                    deleted = True
            
            if not deleted:
                raise HTTPException(status_code=404, detail=f"Vessel {vessel_id} not found")
            
            return {"success": True, "message": f"Vessel {vessel_id} deleted successfully (fallback)"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting vessel {vessel_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete vessel")


# ===== NOMINATION API ENDPOINTS =====




@app.post("/api/nominations")
async def create_nomination_api(
    new_nomination: NewNominationRequest,
    state: Dict = Depends(get_data)
):
    """Create a new nomination and convert it directly to an unscheduled vessel"""
    try:
        # Get terminal ID for database operations
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=400, detail="No active terminal configured")
            
        # Generate a unique vessel ID that won't collide with DB vessel IDs
        vessel_id = _generate_unique_nomination_vessel_id(state)
        
        # Convert cargo dictionaries to Cargo objects
        cargoes = []
        for cargo_data in new_nomination.cargoes:
            if isinstance(cargo_data, dict):
                cargoes.append(Cargo.from_dict(cargo_data))
            else:
                # If it's already a Cargo object
                cargoes.append(cargo_data)
        
        # Create vessel directly from nomination (no intermediate nomination storage)
        from src.models.vessel import VesselBase
        from datetime import datetime, timezone
        
        vessel = VesselBase(
            id=vessel_id,
            name=new_nomination.name,
            vessel_type=VesselType(new_nomination.vessel_type.lower()),
            length=new_nomination.length,
            beam=new_nomination.beam,
            draft=new_nomination.draft,
            deadweight=new_nomination.deadweight,
            cargoes=cargoes,
            status="APPROACHING",  # Put vessel directly in unscheduled state
            priority=new_nomination.priority,
            capacity=new_nomination.capacity,
            width=new_nomination.width,
            metadata=new_nomination.metadata
        )
        
        # Store additional nomination data in vessel metadata for reference
        vessel.metadata.update({
            'cargo_type': new_nomination.cargo_type,
            'flag': new_nomination.flag,
            'imo': new_nomination.imo,
            'mmsi': new_nomination.mmsi,
            'customs_cleared': new_nomination.customs_cleared,
            'last_port': new_nomination.last_port,
            'next_port': new_nomination.next_port,
            'owner': new_nomination.owner,
            'registration_number': new_nomination.registration_number,
            'tug_boat': new_nomination.tug_boat,
            'operation_type': new_nomination.operation_type,
            'has_crane': new_nomination.has_crane,
            'submitted_by': new_nomination.submitted_by
        })
        
        # Set timing information
        if new_nomination.eta:
            vessel.eta = new_nomination.eta
            vessel.arrival_time = new_nomination.eta
        
        if new_nomination.etd:
            vessel.etd = new_nomination.etd
            vessel.departure_time = new_nomination.etd
        
        # Set customer information
        if new_nomination.customer:
            vessel.customer = new_nomination.customer
        
        # NOTE: Do NOT create database vessel record here - nominations should only exist in-memory
        # Database vessels will be created when vessels are actually scheduled/assigned
        # This prevents duplicate vessels (one with NV### ID, one with numeric ID)

        # Persist a nomination record for durability (rehydration on restart)
        try:
            cargo_dicts_db = []
            for c in vessel.cargoes:
                cargo_dicts_db.append({
                    'product': getattr(c, 'product', 'Unknown'),
                    'volume': getattr(c, 'volume', 0.0),
                    'is_loading': getattr(c, 'is_loading', False),
                })
            db.add_nomination({
                'runtime_vessel_id': vessel.id,
                'name': vessel.name,
                'vessel_type': vessel.vessel_type.value.upper(),
                'length': vessel.length,
                'beam': vessel.beam,
                'draft': vessel.draft,
                'deadweight': vessel.deadweight,
                'priority': vessel.priority,
                'capacity': vessel.capacity,
                'width': vessel.width,
                'customer': vessel.customer,
                'status': 'ACTIVE',  # Changed from 'pending' to 'ACTIVE' to match VesselService expectations
                'eta': vessel.eta,
                'etd': vessel.etd,
                'mmsi': vessel.metadata.get('mmsi'),
                'imo': vessel.metadata.get('imo'),
                'cargoes': cargo_dicts_db,
                'metadata': vessel.metadata,
            })
            
            # Log nomination creation for analytics
            try:
                db.log_assignment_change(
                    assignment_id=0,  # No assignment yet
                    old_start_time=None,
                    old_end_time=None,
                    new_start_time=vessel.eta.isoformat() if vessel.eta else None,
                    new_end_time=vessel.etd.isoformat() if vessel.etd else None,
                    reason="New vessel nomination created",
                    vessel_id=vessel.id,
                    vessel_name=vessel.name,
                    jetty_name=None,
                    changed_by="user",
                    terminal_id=terminal_id
                )
                
                # Log detailed change analysis
                db.log_change_analysis(
                    assignment_id=0,
                    change_type='nomination_created',
                    reason_text="New vessel nomination created",
                    original_value=None,
                    new_value=f"Vessel: {vessel.name}, Type: {vessel.vessel_type.value}, ETA: {vessel.eta}",
                    vessel_id=vessel.id,
                    vessel_name=vessel.name,
                    changed_by="user",
                    terminal_id=terminal_id
                )
            except Exception as log_e:
                logger.warning(f"Failed to log nomination creation: {log_e}")
                
        except Exception as e:
            logger.warning(f"Failed persisting nomination record: {e}")

        # DATABASE-FIRST: Use VesselService instead of in-memory state
        try:
            from src.services.vessel_service import VesselService
            vessel_service = VesselService(db)
            
            # Add vessel nomination through service (already done above in db.add_nomination)
            logger.info(f"DATABASE-FIRST: Vessel {vessel.id} added via VesselService")
            
        except Exception as e:
            logger.warning(f"Error using VesselService, falling back to in-memory: {e}")
            # Fallback to in-memory for backward compatibility
            state["vessels"].append(vessel)
        
        # Add vessel to ship tracking system if MMSI is provided
        mmsi = vessel.metadata.get('mmsi')
        if mmsi and state.get("ship_tracking"):
            tracking_service = state["ship_tracking"]
            tracked_ship = tracking_service.add_tracked_ship(
                mmsi=mmsi,
                vessel_name=vessel.name,
                draft=vessel.draft,
                length=vessel.length,
                beam=vessel.beam,
                nomination_id=vessel_id
            )
            logger.info(f"Added vessel {vessel.name} to ship tracking system")
        
        logger.info(f"Created vessel from nomination: {vessel.name} (ID: {vessel_id}) - added to unscheduled vessels")
        
        return {
            "success": True,
            "id": vessel.id,
            "name": vessel.name,
            "status": vessel.status,
            "db_vessel_id": vessel.metadata.get('db_vessel_id'),
            "message": "Nomination created successfully and persisted with cargoes and nomination record"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid nomination data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating nomination: {str(e)}")








@app.get("/api/weather")
async def get_weather_api(
    state: Dict = Depends(get_data),
    forecast: bool = False,
    days: int = 5,
    hours: int = 0
):
    """Get current weather or forecast API endpoint using Open-Meteo API"""
    try:
        weather_api = state["weather_api"]
        terminal = state["terminal"]
        
        # Ensure terminal is proper Terminal object
        if isinstance(terminal, dict):
            from ..api.ml_api import create_fallback_terminal
            terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'location'):
            from ..api.ml_api import create_fallback_terminal
            terminal = create_fallback_terminal()

        # Get location from terminal
        lat, lon = terminal.location or (51.0543, 3.7174)  # Default to Ghent, Belgium if not set

        if forecast:
            if hours > 0:
                # Get hourly forecast for the next specified hours
                forecast_data = weather_api.get_hourly_forecast(lat, lon, hours)
                warnings = weather_api.get_weather_warnings(lat, lon, 1)

                # For testing, if no data is returned, generate some mock data
                if not forecast_data or len(forecast_data) == 0:
                    forecast_data = []
                    current_time = datetime.now(timezone.utc)
                    for i in range(hours):
                        hour_time = current_time + timedelta(hours=i)
                        forecast_data.append({
                            "time": hour_time.isoformat(),
                            "temperature": 15 + i % 5,
                            "wind_speed": 10 + (i % 10),
                            "wind_direction": 180 + i * 10,
                            "humidity": 70 - i % 20,
                            "weather_code": 2,
                            "description": "Partly cloudy",
                            "hour": hour_time.hour,
                            "is_day": 6 <= hour_time.hour <= 18
                        })

                return {
                    "forecast": forecast_data,
                    "warnings": warnings
                }
            else:
                # Get daily forecast
                forecast_data = weather_api.get_forecast(lat, lon, days)
                warnings = weather_api.get_weather_warnings(lat, lon, days)
            
                # Return fallback mock data if needed
                if not forecast_data or len(forecast_data) == 0:
                    forecast_data = []
                    for i in range(days):
                        forecast_date = datetime.now(timezone.utc) + timedelta(days=i)
                        forecast_data.append({
                            "time": forecast_date.strftime("%Y-%m-%dT12:00:00"),
                            "date": forecast_date.strftime("%Y-%m-%d"),
                            "min_temp": 15 + (i % 8),
                            "max_temp": 20 + (i % 8),
                            "max_wind": 8 + (i % 12),
                            "wind_direction": 180 + (i * 30) % 360,
                            "weather_code": 2,
                            "description": "Partly cloudy",
                            "conditions": ["Partly cloudy"],
                            "has_thunderstorm": False
                        })

                # Format forecast data
                formatted_forecast = []
                for entry in forecast_data:
                    formatted_entry = {
                        "date": entry.get("date", entry.get("time", "").split("T")[0]),
                        "min_temp": entry.get("min_temp", entry.get("temperature", 15)),
                        "max_temp": entry.get("max_temp", entry.get("temperature", 20)),
                        "max_wind": entry.get("max_wind", entry.get("wind_speed", 10)),
                        "conditions": entry.get("conditions", [entry.get("description", "Unknown")]),
                        "has_thunderstorm": entry.get("has_thunderstorm", False),
                        "wind_impact": "low",
                        "wind_message": "Normal operations - wind conditions favorable",
                        "thunderstorm_impact": "none",
                        "thunderstorm_message": "No thunderstorms expected"
                    }
                    
                    # Assess wind impact
                    max_wind = formatted_entry["max_wind"]
                    if max_wind >= 17.0:
                        formatted_entry["wind_impact"] = "high"
                        formatted_entry["wind_message"] = "Vessel operations may be restricted due to high winds"
                    elif max_wind >= 12.0:
                        formatted_entry["wind_impact"] = "moderate"
                        formatted_entry["wind_message"] = "Exercise caution during vessel approach and mooring"
                    
                    # Assess thunderstorm impact
                    if formatted_entry["has_thunderstorm"]:
                        formatted_entry["thunderstorm_impact"] = "high"
                        formatted_entry["thunderstorm_message"] = "Pumping operations will be suspended during thunderstorms"
                    
                    formatted_forecast.append(formatted_entry)

                # Format warnings
                formatted_warnings = []
                for warning in warnings:
                    if "wind" in warning.get("reason", "").lower() or "storm" in warning.get("reason", "").lower():
                        formatted_warnings.append({
                            "title": warning.get("reason", "Weather Warning"),
                            "description": warning.get("description", warning.get("reason", "")),
                            "type": "wind" if "wind" in warning.get("reason", "").lower() else "storm",
                            "severity": warning.get("severity", "moderate"),
                            "timestamp": warning.get("timestamp", "")
                        })

                return {
                    "forecast": formatted_forecast,
                    "warnings": formatted_warnings
                }
        else:
            # Get current weather
            current_weather = weather_api.get_current_weather(lat, lon)
            operations_status = weather_api.is_weather_suitable_for_operations(lat, lon)

            # For testing, if no data is returned, generate mock data
            if not current_weather or not current_weather.get('temperature'):
                current_weather = {
                    "temperature": 18.5,
                    "wind_speed": 14.2,
                    "wind_direction": 225,
                    "humidity": 65,
                    "pressure": 1015,
                    "visibility": 10000,
                    "weather_code": 2,
                    "description": "Partly cloudy",
                    "is_day": True
                }

            # Format current weather data
            formatted_current = {
                "temperature": current_weather.get("temperature", 18.5),
                "conditions": current_weather.get("description", "Partly cloudy"),
                "wind_speed": current_weather.get("wind_speed", 14.2),
                "wind_direction": current_weather.get("wind_direction", 225),
                "humidity": current_weather.get("humidity", 65),
                "pressure": current_weather.get("pressure", 1015),
                "visibility": current_weather.get("visibility", 10000),
                "weather_code": current_weather.get("weather_code", 2),
                "description": current_weather.get("description", "Partly cloudy"),
                "is_day": current_weather.get("is_day", True),
                "timestamp": current_weather.get("timestamp", datetime.now(timezone.utc).isoformat())
            }

            # Format operations status
            formatted_status = {
                "suitable": operations_status.get("suitable", True),
                "conditions": "good" if operations_status.get("suitable", True) else "restricted",
                "message": "Operations conditions are favorable" if operations_status.get("suitable", True) else
                          f"Operations may be affected due to {operations_status.get('reason', 'weather conditions')}",
                "wind": {
                    "safe": formatted_current["wind_speed"] < 17.0,
                    "message": "Wind conditions are within safe limits" if formatted_current["wind_speed"] < 17.0 else
                              f"High wind conditions ({formatted_current['wind_speed']} m/s)"
                },
                "approach": {
                    "safe": operations_status.get("suitable", True),
                    "message": "Vessel approach conditions are good" if operations_status.get("suitable", True) else
                              f"Vessel approach may be difficult due to {operations_status.get('reason', 'weather conditions')}"
                }
            }

            return {
                "current_weather": formatted_current,
                "operations_status": formatted_status
            }

    except Exception as e:
        logger.error(f"Error in weather API endpoint: {e}")
        # Return mock data as fallback
        return {
            "current_weather": {
                "temperature": 18.5,
                "conditions": "Partly cloudy",
                "wind_speed": 14.2,
                "wind_direction": 225,
                "humidity": 65,
                "pressure": 1015,
                "visibility": 10000,
                "weather_code": 2,
                "description": "Partly cloudy",
                "is_day": True,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            "operations_status": {
                "suitable": True,
                "conditions": "good",
                "message": "Operations conditions are favorable (fallback data)",
                "wind": {
                    "safe": True,
                    "message": "Wind conditions are within safe limits"
                },
                "approach": {
                    "safe": True,
                    "message": "Vessel approach conditions are good"
                }
            }
        }


@app.get("/api/vessels/nearby")
async def get_nearby_vessels_api(state: Dict = Depends(get_data)):
    """Get vessels near the terminal from AIS Stream API endpoint"""
    aisstream = state["aisstream"]
    terminal = state["terminal"]
    
    # Ensure terminal is proper Terminal object
    if isinstance(terminal, dict):
        from ..api.ml_api import create_fallback_terminal
        terminal = create_fallback_terminal()
    elif not hasattr(terminal, 'location'):
        from ..api.ml_api import create_fallback_terminal
        terminal = create_fallback_terminal()

    # Get location from terminal
    lat, lon = terminal.location or (51.0543, 3.7174)  # Default to Ghent, Belgium if not set

    # Get vessels near the terminal
    vessels = aisstream.get_vessels_near_port(lat, lon, radius=50)

    return vessels


@app.get("/api/aisstream/status")
async def get_aisstream_status(state: Dict = Depends(get_data)):
    """Get AIS Stream connection status"""
    aisstream = state["aisstream"]
    
    status = aisstream.get_connection_status()
    # Add diagnostics while keeping a flat shape for UI
    return {
        "service": "AIS Stream",
        "vessels_tracked": status.get("vessels_tracked", 0),
        "connected": status.get("connected", False),
        "use_mock": status.get("use_mock", False),
        "total_messages_received": status.get("total_messages_received", 0),
        "last_successful_connection": status.get("last_successful_connection"),
        "log_hint": "info-every-200",
        "reconnect_interval_seconds": getattr(aisstream, "reconnect_interval", None)
    }


@app.post("/api/assistant")
async def assistant_api(request: AssistantRequest, state: Dict = Depends(get_data)):
    """Assistant API endpoint using Claude interface"""
    claude = state["claude_interface"]

    try:
        # Get current weather data
        weather_data = state["weather_api"].get_current_weather(51.0543, 3.7174)  # Ghent, Belgium coordinates

        # Ensure terminal is proper Terminal object for context
        terminal = state["terminal"]
        if isinstance(terminal, dict):
            from ..api.ml_api import create_fallback_terminal
            terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'name'):
            from ..api.ml_api import create_fallback_terminal
            terminal = create_fallback_terminal()
        
        # Build additional context: database, AIS tracking, ML status, settings
        database_summary: Dict[str, Any] = {}
        try:
            # Backend information and basic counts
            backend = "postgres"  # PostgreSQL-only architecture
            active_terminal_id = db.get_active_terminal_id()
            # Safe count helpers
            try:
                vessels_db = db.get_vessels(active_terminal_id)
            except Exception:
                vessels_db = []
            try:
                jetties_db = db.get_jetties(active_terminal_id)
            except Exception:
                jetties_db = []
            try:
                tanks_db = db.get_tanks(active_terminal_id)
            except Exception:
                tanks_db = []
            try:
                pumps_db = db.get_pumps(active_terminal_id)
            except Exception:
                pumps_db = []
            try:
                surveyors_db = db.get_surveyors(active_terminal_id)
            except Exception:
                surveyors_db = []
            try:
                assignments_db = db.get_assignments(active_terminal_id)
            except Exception:
                assignments_db = []

            database_summary = {
                "backend": backend,
                "active_terminal_id": active_terminal_id,
                "counts": {
                    "vessels": len(vessels_db or []),
                    "jetties": len(jetties_db or []),
                    "tanks": len(tanks_db or []),
                    "pumps": len(pumps_db or []),
                    "surveyors": len(surveyors_db or []),
                    "assignments": len(assignments_db or []),
                },
            }
        except Exception as e:
            logger.warning(f"Failed to build database summary: {e}")

        # AIS tracking context (health + a few nearby vessels)
        ais_summary: Dict[str, Any] = {}
        try:
            ais_client = state.get("aisstream")
            if ais_client:
                ais_health = ais_client.get_health_status()
                # Determine coordinates for vicinity search
                try:
                    lat = getattr(terminal, "latitude", None) or getattr(terminal, "location_lat", 51.0543)
                    lon = getattr(terminal, "longitude", None) or getattr(terminal, "location_lon", 3.7174)
                except Exception:
                    lat, lon = 51.0543, 3.7174
                nearby = ais_client.get_vessels_near_port(lat, lon, radius=50) if hasattr(ais_client, "get_vessels_near_port") else []
                ais_summary = {
                    "connected": ais_health.get("connected"),
                    "use_mock": ais_health.get("use_mock"),
                    "vessels_tracked": ais_health.get("vessels_tracked"),
                    "nearby_sample": [
                        {
                            "name": v.get("name"),
                            "mmsi": v.get("mmsi"),
                            "type": v.get("vessel_type") or v.get("vessel_type_text"),
                            "speed": (v.get("position") or {}).get("speed"),
                        }
                        for v in (nearby or [])[:8]
                    ],
                }
        except Exception as e:
            logger.warning(f"Failed to build AIS summary: {e}")

        # ML service status
        ml_status: Dict[str, Any] = {}
        try:
            from ..ml.prediction_service import MLPredictionService
            if state.get("ml_service") is None:
                state["ml_service"] = MLPredictionService()
            ml_service = state["ml_service"]
            # Summarize available models and metadata
            try:
                models_available = {name: (model is not None) for name, model in (ml_service.models or {}).items()}
            except Exception:
                models_available = {}
            ml_status = {
                "models_available": models_available,
                "last_updated": getattr(ml_service, "model_metadata", {}).get("last_updated"),
            }
        except Exception as e:
            logger.warning(f"Failed to build ML status: {e}")

        # Settings context (non-sensitive DB settings + runtime settings)
        settings_context: Dict[str, Any] = {}
        try:
            db_settings = {}
            for s in db.get_settings() or []:
                try:
                    if not s.get("is_sensitive"):
                        db_settings[s["key"]] = s.get("value")
                except Exception:
                    pass
            settings_context = {
                "runtime": state.get("settings", {}),
                "db": db_settings,
            }
        except Exception as e:
            logger.warning(f"Failed to load settings for context: {e}")
        
        # Create context with current state
        context = {
            "terminal": terminal,
            "vessels": state["vessels"],
            "schedule": state["schedule"],
            "weather": weather_data,
            "database": database_summary,
            "ais_tracking": ais_summary,
            "ml_status": ml_status,
            "settings": settings_context,
        }

        # Query Claude with the user's message and context
        response = claude.query(request.message, context)

        return {
            "response": response
        }
    except Exception as e:
        logger.error(f"Error in assistant API: {e}")
        return {
            "response": f"I'm sorry, I encountered an error: {str(e)}. Please try again later."
        }


@app.get("/api/assistant/status")
async def assistant_status_api(state: Dict = Depends(get_data)):
    """Get the status of the Claude API (mock or live)"""
    claude = state["claude_interface"]
    
    mode = "mock" if claude.use_mock else "live"
    
    return {
        "mode": mode,
        "model": claude.model,
        "using_real_api": not claude.use_mock
    }


# === Ship Tracking API Endpoints ===

@app.get("/api/tracking/summary")
async def get_tracking_summary(state: Dict = Depends(get_data)):
    """Get ship tracking summary with geofence information"""
    tracking_service = state.get("ship_tracking")
    if not tracking_service:
        raise HTTPException(status_code=503, detail="Ship tracking service not available")
    
    return tracking_service.get_tracking_summary()


class TrackShipRequest(BaseModel):
    """Payload to start or update tracking for a ship"""
    mmsi: str
    vessel_name: Optional[str] = None
    length: Optional[float] = None
    beam: Optional[float] = None
    draft: Optional[float] = None
    nomination_id: Optional[str] = None


class TrackShipBatchRequest(BaseModel):
    ships: List[TrackShipRequest]


@app.post("/api/tracking/track")
async def track_ship(req: TrackShipRequest, state: Dict = Depends(get_data)):
    """Ensure a ship with given MMSI is tracked (idempotent)."""
    tracking_service = state.get("ship_tracking")
    if not tracking_service:
        raise HTTPException(status_code=503, detail="Ship tracking service not available")

    mmsi = str(req.mmsi)
    if not mmsi:
        raise HTTPException(status_code=400, detail="MMSI is required")

    # If already tracked, update metadata; otherwise add
    if mmsi in tracking_service.tracked_ships:
        ship = tracking_service.tracked_ships[mmsi]
        if req.vessel_name:
            ship.vessel_name = req.vessel_name
        if req.length is not None:
            ship.length = req.length
        if req.beam is not None:
            ship.beam = req.beam
        if req.draft is not None:
            ship.draft = req.draft
        if req.nomination_id:
            ship.nomination_id = req.nomination_id
        updated = True
    else:
        tracking_service.add_tracked_ship(
            mmsi=mmsi,
            vessel_name=req.vessel_name or mmsi,
            draft=req.draft or 0.0,
            length=req.length or 0.0,
            beam=req.beam or 0.0,
            nomination_id=req.nomination_id
        )
        updated = False

    # Re-enable AIS fallback once explicit tracking begins post-reset
    if state.get("disable_ais_fallback"):
        state["disable_ais_fallback"] = False
    return {"success": True, "mmsi": mmsi, "updated": updated}


@app.post("/api/tracking/track-batch")
async def track_ships_batch(batch: TrackShipBatchRequest, state: Dict = Depends(get_data)):
    """Batch ensure ships are tracked; returns counts of added/updated"""
    tracking_service = state.get("ship_tracking")
    if not tracking_service:
        raise HTTPException(status_code=503, detail="Ship tracking service not available")

    added = 0
    updated = 0
    for ship_req in (batch.ships or []):
        mmsi = str(ship_req.mmsi)
        if not mmsi:
            continue
        if mmsi in tracking_service.tracked_ships:
            ship = tracking_service.tracked_ships[mmsi]
            if ship_req.vessel_name:
                ship.vessel_name = ship_req.vessel_name
            if ship_req.length is not None:
                ship.length = ship_req.length
            if ship_req.beam is not None:
                ship.beam = ship_req.beam
            if ship_req.draft is not None:
                ship.draft = ship_req.draft
            if ship_req.nomination_id:
                ship.nomination_id = ship_req.nomination_id
            updated += 1
        else:
            tracking_service.add_tracked_ship(
                mmsi=mmsi,
                vessel_name=ship_req.vessel_name or mmsi,
                draft=ship_req.draft or 0.0,
                length=ship_req.length or 0.0,
                beam=ship_req.beam or 0.0,
                nomination_id=ship_req.nomination_id
            )
            added += 1

    # Re-enable AIS fallback once explicit tracking begins post-reset
    if (added + updated) > 0 and state.get("disable_ais_fallback"):
        state["disable_ais_fallback"] = False
    return {"success": True, "added": added, "updated": updated, "total": added + updated}


@app.get("/api/tracking/ships")
async def get_tracked_ships(zone: Optional[str] = Query(None, description="Filter by geofence zone (2_hour, 4_hour, terminal)"),
                           only_liquid: bool = Query(False, description="Return only likely liquid cargo ships (oil/chemical/gas tankers, inland tankers) when AIS data available"),
                           selected_mmsi: Optional[str] = Query(None, description="Optional: highlight/ensure presence of this MMSI if no AIS data yet"),
                           state: Dict = Depends(get_data)):
    """Get list of tracked ships, optionally filtered by zone"""
    tracking_service = state.get("ship_tracking")
    if not tracking_service:
        raise HTTPException(status_code=503, detail="Ship tracking service not available")
    
    if zone:
        from ..services.ship_tracking_service import GeofenceZone
        try:
            zone_enum = GeofenceZone(zone)
            ships = tracking_service.get_ships_in_zone(zone_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid zone: {zone}")
    else:
        ships = list(tracking_service.tracked_ships.values())
    
    # Proactively kick-start AIS streaming when no tracking data is available yet,
    # so frontend maps (e.g., nomination page) can begin to populate with nearby vessels.
    try:
        aisstream = state.get("aisstream")
        terminal = state.get("terminal")
        if aisstream and not getattr(aisstream, "use_mock", False):
            no_cache = len(getattr(aisstream, "vessel_data", {}) or {}) == 0
            if no_cache and not getattr(aisstream, "connection_active", False):
                try:
                    # Ensure we receive both static and position reports, including Class B variants
                    aisstream.set_message_types([
                        "PositionReport",
                        "ExtendedClassBPositionReport",
                        "StandardClassBPositionReport",
                        "ShipStaticData",
                    ])
                except Exception:
                    pass
                # Narrow subscription to terminal region when available
                try:
                    if terminal and hasattr(terminal, "location"):
                        lat, lon = terminal.location
                        # Use a generous radius to capture approaches via Westerschelde
                        aisstream.set_subscription_region(lat, lon, radius_km=60.0)
                except Exception:
                    pass
                # Short on-demand session; data will accumulate over subsequent calls
                aisstream.start_streaming_on_demand(duration_minutes=3)
    except Exception:
        # Never fail the endpoint due to AIS bootstrap issues
        pass
    
    # Fallback: if no tracked ships are available yet, surface AIS nearby vessels
    # so that frontend maps can still render ships. This does not mutate tracking state.
    def _has_valid_positions(ship_list):
        try:
            for s in ship_list:
                pos = getattr(s, "current_position", None)
                if pos and getattr(pos, "latitude", None) is not None and getattr(pos, "longitude", None) is not None:
                    return True
        except Exception:
            pass
        return False

    if not ships or not _has_valid_positions(ships):
        try:
            aisstream = state.get("aisstream")
            terminal = state.get("terminal")
            # Ensure we have a terminal location
            if terminal and hasattr(terminal, 'location'):
                lat, lon = terminal.location
            else:
                lat, lon = (51.34543250288062, 3.751466718019277)  # EVOS Terneuzen default

            nearby = aisstream.get_vessels_near_port(lat, lon, radius=60) if aisstream else []

            # If a specific MMSI was requested and not present yet, attempt to synthesize it
            def _ensure_selected_in_list(vessels: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
                try:
                    if not selected_mmsi:
                        return vessels
                    found = any(str(v.get("mmsi")) == str(selected_mmsi) for v in vessels)
                    if found:
                        return vessels
                    # Try to enrich from AIS cache if available
                    sel = None
                    if aisstream and hasattr(aisstream, "get_vessel_by_mmsi"):
                        sel = aisstream.get_vessel_by_mmsi(str(selected_mmsi))
                    if sel:
                        vessels = list(vessels) + [sel]
                    else:
                        # Create a minimal synthetic vessel near the terminal to allow map focus/highlight
                        vessels = list(vessels) + [{
                            "mmsi": str(selected_mmsi),
                            "name": "Selected Vessel",
                            "position": {
                                "latitude": lat + 0.01,
                                "longitude": lon + 0.01,
                                "speed": 0.0,
                                "course": 0.0,
                                "timestamp": datetime.now(timezone.utc).isoformat()
                            }
                        }]
                except Exception:
                    pass
                return vessels

            nearby = _ensure_selected_in_list(nearby)

            def _format_ais(v):
                pos = v.get("position") or {}
                return {
                    "mmsi": v.get("mmsi"),
                    "vessel_name": v.get("name") or "Unknown",
                    "nomination_id": None,
                    "current_zone": "4_hour",
                    "estimated_eta": v.get("eta"),
                    "distance_to_terminal": None,
                    "current_position": {
                        "latitude": pos.get("latitude"),
                        "longitude": pos.get("longitude"),
                        "speed": pos.get("speed"),
                        "course": pos.get("course"),
                        "timestamp": pos.get("timestamp") or datetime.now(timezone.utc).isoformat()
                    } if pos else None,
                    "vessel_details": {
                        "length": v.get("length"),
                        "beam": v.get("width"),
                        "draft": v.get("draft")
                    },
                    "constraints": {
                        "requires_high_tide": False,
                        "requires_lock_passage": False,
                        "tidal_constraints": []
                    },
                    "last_update": state.get("aisstream").last_update.get(v.get("mmsi")).isoformat() if state.get("aisstream") and v.get("mmsi") in state.get("aisstream").last_update else None
                }

            fallback_payload = {
                "ships": [_format_ais(v) for v in nearby],
                "total_count": len(nearby)
            }

            # If we have any nearby AIS vessels, return them now
            if fallback_payload["total_count"]:
                return fallback_payload
        except Exception:
            # Non-fatal: keep going with empty list
            pass

    def _format_ship(ship):
        return {
                "mmsi": ship.mmsi,
                "vessel_name": ship.vessel_name,
                "nomination_id": ship.nomination_id,
                "current_zone": ship.current_zone.value,
                "estimated_eta": ship.estimated_eta.isoformat() if ship.estimated_eta else None,
                "distance_to_terminal": ship.distance_to_terminal,
                "current_position": {
                    "latitude": ship.current_position.latitude,
                    "longitude": ship.current_position.longitude,
                    "speed": ship.current_position.speed,
                    "course": ship.current_position.course,
                    "timestamp": ship.current_position.timestamp.isoformat()
                } if ship.current_position else None,
                "vessel_details": {
                    "length": ship.length,
                    "beam": ship.beam,
                    "draft": ship.draft
                },
                "constraints": {
                    "requires_high_tide": ship.requires_high_tide,
                    "requires_lock_passage": ship.requires_lock_passage,
                    "tidal_constraints": ship.tidal_constraints
                },
                "last_update": ship.last_update.isoformat()
        }

    # Optional liquid-only filter using known nomination data when present
    # If we have the vessel in AIS cache, we can screen on its ShipStaticData.Type too
    if only_liquid:
        try:
            aisstream = state.get("aisstream")
        except Exception:
            aisstream = None
        filtered = []
        for ship in ships:
            if aisstream and ship.mmsi in getattr(aisstream, 'vessel_data', {}):
                t = str(aisstream.vessel_data[ship.mmsi].get('vessel_type', '')).lower()
                if not ("tanker" in t or "oil" in t or "chemical" in t or "inland" in t or "liquid" in t or "gas" in t or "lng" in t or "lpg" in t):
                    continue
            filtered.append(ship)
        ships = filtered

    return {
        "ships": [ _format_ship(ship) for ship in ships ],
        "total_count": len(ships)
    }


@app.post("/api/tracking/update/{mmsi}")
async def update_ship_position(mmsi: str, position_data: Dict[str, Any], 
                              state: Dict = Depends(get_data)):
    """Update a ship's position (typically called by AIS integration)"""
    tracking_service = state.get("ship_tracking")
    if not tracking_service:
        raise HTTPException(status_code=503, detail="Ship tracking service not available")
    
    try:
        position = ShipPosition(
            mmsi=mmsi,
            latitude=position_data["latitude"],
            longitude=position_data["longitude"],
            course=position_data.get("course", 0),
            speed=position_data.get("speed", 0),
            timestamp=datetime.fromisoformat(position_data.get("timestamp", datetime.now(timezone.utc).isoformat())),
            heading=position_data.get("heading"),
            status=position_data.get("status", "Unknown")
        )
        
        updated_ship = tracking_service.update_ship_position(mmsi, position)
        
        if updated_ship:
            return {
                "success": True,
                "mmsi": mmsi,
                "updated_eta": updated_ship.estimated_eta.isoformat() if updated_ship.estimated_eta else None,
                "current_zone": updated_ship.current_zone.value,
                "distance_to_terminal": updated_ship.distance_to_terminal
            }
        else:
            raise HTTPException(status_code=404, detail=f"Ship with MMSI {mmsi} not found in tracking system")
            
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error updating ship position: {str(e)}")


# === Tidal Information API Endpoints ===

@app.get("/api/tidal/current")
async def get_current_water_level(station: str = Query("VLISSGN", description="RWS station code"),
                                 state: Dict = Depends(get_data)):
    """Get current water level at a tidal station"""
    tidal_service = state.get("tidal_service")
    if not tidal_service:
        raise HTTPException(status_code=503, detail="Tidal service not available")
    
    try:
        tide_data = await tidal_service.get_current_water_level(station)
        
        if tide_data:
            return {
                "station": tide_data.station,
                "water_level": tide_data.water_level,
                "measurement_type": tide_data.measurement_type,
                "timestamp": tide_data.datetime.isoformat(),
                "navigation_safe_10m": tidal_service.is_navigation_safe(tide_data.water_level, 10.0),
                "navigation_safe_12m": tidal_service.is_navigation_safe(tide_data.water_level, 12.0),
                "navigation_safe_15m": tidal_service.is_navigation_safe(tide_data.water_level, 15.0)
            }
        else:
            raise HTTPException(status_code=404, detail=f"No tidal data available for station {station}")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tidal data: {str(e)}")


@app.get("/api/tidal/forecast")
async def get_tidal_forecast(
    station: str = Query("VLISSGN", description="RWS station code"),
    hours: int = Query(24, ge=1, le=72, description="Hours ahead to fetch forecast"),
    state: Dict = Depends(get_data)
):
    """Get tidal forecast and summary (next high/low tide) for a station."""
    tidal_service = state.get("tidal_service")
    if not tidal_service:
        raise HTTPException(status_code=503, detail="Tidal service not available")

    try:
        forecast_data = await tidal_service.get_tide_forecast(station_code=station, hours_ahead=hours)
        # Build plain series for the UI
        series = [{
            "time": td.datetime.isoformat(),
            "water_level": td.water_level
        } for td in forecast_data]

        # Find next local maxima/minima after now
        next_high = None
        next_low = None
        now_ts = datetime.now(timezone.utc)
        # Ensure sorted
        forecast_data = sorted(forecast_data, key=lambda x: x.datetime)
        for i in range(1, len(forecast_data) - 1):
            prev = forecast_data[i - 1]
            cur = forecast_data[i]
            nxt = forecast_data[i + 1]
            if cur.datetime <= now_ts:
                continue
            # Peak: increasing then decreasing
            if (cur.water_level - prev.water_level) > 0 and (nxt.water_level - cur.water_level) < 0 and next_high is None:
                next_high = {"time": cur.datetime.isoformat(), "water_level": cur.water_level}
            # Trough: decreasing then increasing
            if (cur.water_level - prev.water_level) < 0 and (nxt.water_level - cur.water_level) > 0 and next_low is None:
                next_low = {"time": cur.datetime.isoformat(), "water_level": cur.water_level}
            if next_high is not None and next_low is not None:
                break

        return {
            "station": station,
            "forecast": series,
            "next_high": next_high,
            "next_low": next_low,
            "navigation_levels": getattr(tidal_service, "navigation_levels", {})
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tidal forecast: {str(e)}")


@app.get("/api/locks/debug")
async def debug_locks(state: Dict = Depends(get_data)):
    """Debug endpoint to show available locks"""
    lock_service = state.get("lock_service")
    if not lock_service:
        return {"error": "Lock service not available"}
    
    try:
        await lock_service._ensure_wfs_loaded()
    except Exception:
        pass
    return {
        "available_locks": {
            name: {"code": lock.code, "name": lock.name}
            for name, lock in lock_service.locks.items()
        }
    }

@app.get("/api/locks/debug")
async def debug_locks(state: Dict = Depends(get_data)):
    """Debug endpoint to check lock configuration"""
    lock_service = state.get("lock_service")
    if not lock_service:
        return {"error": "Lock service not available"}
    
    try:
        await lock_service._ensure_wfs_loaded()
    except Exception:
        pass
    locks_info = []
    for lock_name, lock_data in lock_service.locks.items():
        locks_info.append({
            "name": lock_name,
            "code": lock_data.code,
            "full_name": lock_data.name
        })
    
    return {
        "total_locks": len(lock_service.locks),
        "locks": locks_info
    }

@app.get("/api/locks")
async def list_locks(state: Dict = Depends(get_data)):
    """List available locks and their short codes."""
    lock_service = state.get("lock_service")
    if not lock_service:
        raise HTTPException(status_code=503, detail="Lock service not available")
    # Ensure WFS has been loaded at least once
    try:
        await lock_service._ensure_wfs_loaded()
    except Exception:
        pass
    return [
        {
            "code": lock.code,
            "name": lock.name,
            "key": key
        }
        for key, lock in lock_service.locks.items()
    ]

@app.get("/api/locks/status/{lock_code}")
async def get_lock_status(lock_code: str, state: Dict = Depends(get_data)):
    """Get current status of a lock"""
    lock_service = state.get("lock_service")
    if not lock_service:
        raise HTTPException(status_code=503, detail="Lock service not available")
    
    try:
        # Ensure WFS data available
        try:
            await lock_service._ensure_wfs_loaded()
        except Exception:
            pass
        try:
            lock_info = await lock_service.get_lock_status(lock_code.upper())
        except Exception as e:
            # Map typed service exceptions to HTTP errors
            from ..services.lock_service import LockService as _LS
            msg = str(e)
            if isinstance(e, _LS.LockNotFoundError) or msg.startswith("Lock ") and " not found" in msg:
                raise HTTPException(status_code=404, detail=msg)
            if isinstance(e, _LS.ServiceUnavailableError) or msg.startswith("Unable to fetch"):
                raise HTTPException(status_code=503, detail=msg)
            # Unknown error
            raise
        
        if lock_info:
            return {
                "name": lock_info.name,
                "code": lock_info.code,
                "location": {"latitude": lock_info.location[0], "longitude": lock_info.location[1]},
                "status": lock_info.status.value,
                "chambers": lock_info.chambers,
                "max_dimensions": {
                    "length": lock_info.max_length,
                    "beam": lock_info.max_beam,
                    "draft": lock_info.max_draft,
                    "air_draft": lock_info.max_air_draft
                },
                "next_opening": lock_info.next_opening.isoformat() if lock_info.next_opening else None,
                "contact_info": lock_info.contact_info
            }
        else:
            # Provide debug info about available locks
            available_codes = [lock.code for lock in lock_service.locks.values()]
            raise HTTPException(
                status_code=404, 
                detail=f"Lock {lock_code} not found. Available lock codes: {available_codes}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching lock status: {str(e)}")


@app.post("/api/locks/check-passage")
async def check_vessel_lock_passage(vessel_data: Dict[str, Any], state: Dict = Depends(get_data)):
    """Check if a vessel can pass through locks on a route"""
    lock_service = state.get("lock_service")
    if not lock_service:
        raise HTTPException(status_code=503, detail="Lock service not available")
    
    try:
        # Ensure WFS lock data is loaded so dimensions are present where available
        try:
            await lock_service._ensure_wfs_loaded()
        except Exception:
            pass
        length = vessel_data.get("length", 0)
        beam = vessel_data.get("beam", 0)
        draft = vessel_data.get("draft", 0)
        air_draft = vessel_data.get("air_draft")
        
        # Check all major locks
        results = {}
        for lock_code in ["TERNEUZEN", "HANSWEERT", "KREEKRAK"]:
            can_pass, restrictions = lock_service.can_vessel_pass(
                lock_code, length, beam, draft, air_draft
            )
            results[lock_code] = {
                "can_pass": can_pass,
                "restrictions": restrictions
            }
        
        return {
            "vessel_dimensions": {
                "length": length,
                "beam": beam,
                "draft": draft,
                "air_draft": air_draft
            },
            "lock_compatibility": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking lock passage: {str(e)}")


# Note: Nomination processing function removed - nominations now convert directly to vessels


# Background task function for optimization
async def run_optimization(
    state: Dict,
    horizon_days: int,
    time_granularity_hours: int,
    weight_throughput: Optional[float],
    weight_demurrage: Optional[float],
    weight_priority: Optional[float],
    weight_weather: Optional[float],
    weight_jetty_balance: Optional[float] = 0.0,
    force_assign_all: bool = False,
    include_mock_assignments: bool = False,
    approach_time_hours: int = 2,
    free_wait_buffer_hours: int = 1,
    fill_unassigned: bool = False,
    preserve_locked: bool = True,
    vessel_filter: Optional[List[str]] = None,
    time_window_start: Optional[datetime] = None,
    time_window_end: Optional[datetime] = None
):
    """Run optimization in the background"""
    try:
        logger.info("Starting optimization...")

        # Create or reuse ML prediction service (singleton on state)
        try:
            from src.ml.prediction_service import MLPredictionService
            if state.get("ml_service") is None:
                state["ml_service"] = MLPredictionService()
            ml_service = state["ml_service"]
        except Exception:
            from src.ml.prediction_service import MLPredictionService
            ml_service = MLPredictionService()
        
        # Ensure terminal is proper Terminal object for scheduler
        terminal = state["terminal"]
        def _terminal_has_operational_arms(t) -> bool:
            try:
                if not hasattr(t, 'jetties') or not t.jetties:
                    return False
                for j in t.jetties:
                    arms = getattr(j, 'loading_arms', []) or []
                    if any(getattr(a, 'is_operational', False) and getattr(a, 'compatible_products', []) for a in arms):
                        return True
                return False
            except Exception:
                return False

        if isinstance(terminal, dict) or not hasattr(terminal, 'jetties') or not _terminal_has_operational_arms(terminal):
            # Try to hydrate a full Terminal object from the DB first
            try:
                from ..database import db as _db_mod
                import src.database_extension  # ensures get_terminal_as_object is patched
                terminal_obj = _db_mod.get_terminal_as_object()
                if terminal_obj and _terminal_has_operational_arms(terminal_obj):
                    logger.info("Hydrated Terminal object from DB for scheduler")
                    terminal = terminal_obj
                else:
                    logger.warning("DB hydration produced incomplete terminal; falling back to business-rules terminal")
                    from ..api.ml_api import create_fallback_terminal
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.warning(f"Terminal hydration failed: {e}; using fallback terminal")
                from ..api.ml_api import create_fallback_terminal
                terminal = create_fallback_terminal()
        
        # Build reserved intervals
        reserved_by_jetty: Dict[str, List[Tuple[datetime, datetime]]] = {}
        try:
            terminal_id = db.get_active_terminal_id()
            existing = db.get_assignments(terminal_id)
            # Preserve locked assignments regardless of include_mock_assignments when preserve_locked is True
            if preserve_locked:
                for a in existing:
                    ls = (a.get('lock_status') or 'UNLOCKED').upper()
                    if ls != 'UNLOCKED':
                        try:
                            st = datetime.fromisoformat(a.get('start_time'))
                            et = datetime.fromisoformat(a.get('end_time'))
                        except Exception:
                            continue
                        if not st or not et or et <= st:
                            continue
                        jetty_key = a.get('jetty_name') or a.get('jetty_id') or ''
                        reserved_by_jetty.setdefault(str(jetty_key), []).append((st, et))
            # Optionally include all existing assignments as reserved (legacy behavior)
            if include_mock_assignments:
                for a in existing:
                    try:
                        # Normalize status to decide if it should block time
                        st_norm = normalize_status(a.get('status', ''))
                    except Exception:
                        st_norm = (a.get('status') or '').upper()
                    if st_norm in ['IN_PROGRESS', 'ACTIVE', 'SCHEDULED', 'APPROVED', 'PENDING_APPROVAL', 'COMPLETED']:
                        try:
                            st = datetime.fromisoformat(a.get('start_time'))
                            et = datetime.fromisoformat(a.get('end_time'))
                        except Exception:
                            continue
                        if st and et and et > st:
                            jetty_key = a.get('jetty_name') or a.get('jetty_id') or ''
                            reserved_by_jetty.setdefault(str(jetty_key), []).append((st, et))
        except Exception as e:
            logger.warning(f"Failed to load existing assignments for reserved intervals: {e}")

        # DATABASE-FIRST: Get planning vessels from database sources
        planning_vessels = []
        try:
            # Initialize vessel service for database-first vessel access
            from src.services.vessel_service import VesselService
            vessel_service = VesselService(db)
            
            # Get all available vessels from database (nominations + cancelled assignments)
            terminal_id = db.get_active_terminal_id()
            if terminal_id:
                planning_vessels = vessel_service.get_available_vessels(terminal_id)
                logger.info(f"DATABASE-FIRST: Found {len(planning_vessels)} vessels from database sources")
            else:
                logger.warning("No active terminal found - cannot get vessels from database")
                
        except Exception as e:
            logger.error(f"Error getting vessels from database-first approach: {e}")
            # Fallback to in-memory vessels only if database approach fails
            try:
                from src.utils.status_utils import normalize_status as _norm_status
            except Exception:
                _norm_status = lambda s: (s or "").upper()
            
            planning_vessels = []
            for v in state.get("vessels", []) or []:
                if _norm_status(getattr(v, 'status', '')) in ["EN_ROUTE", "APPROACHING", "ARRIVED", "WAITING"]:
                    planning_vessels.append(v)
            
            logger.warning(f"Fallback to in-memory vessels: {len(planning_vessels)} vessels found")

        # Apply vessel filter for selective optimization
        try:
            if vessel_filter:
                vf = set(str(x) for x in vessel_filter)
                planning_vessels = [v for v in planning_vessels if str(getattr(v, 'id', '')) in vf]
        except Exception:
            pass

        # Log vessel details for debugging
        logger.info(f"=== OPTIMIZATION DEBUG INFO ===")
        logger.info(f"Total planning vessels found: {len(planning_vessels)}")
        for i, vessel in enumerate(planning_vessels):
            logger.info(f"  Vessel {i+1}: ID={getattr(vessel, 'id', 'unknown')}, "
                       f"Name={getattr(vessel, 'name', 'unknown')}, "
                       f"Status={getattr(vessel, 'status', 'unknown')}, "
                       f"Type={getattr(vessel, 'vessel_type', 'unknown')}, "
                       f"Source={getattr(vessel, 'metadata', {}).get('source', 'in-memory')}")

        # Create scheduler
        scheduler = JettyScheduler(
            terminal,
            planning_vessels,
            state["weather_api"],
            ml_service=ml_service,
            horizon_days=horizon_days,
            time_granularity_hours=time_granularity_hours,
            force_assign_all=force_assign_all,
            use_ml_predictions=True,
            reserved_intervals_by_jetty=reserved_by_jetty,
            approach_time_hours=approach_time_hours,
            free_wait_buffer_hours=free_wait_buffer_hours
        )

        # Set custom weights if provided
        if weight_throughput is not None:
            scheduler.weight_throughput = weight_throughput

        if weight_demurrage is not None:
            scheduler.weight_demurrage = weight_demurrage

        if weight_priority is not None:
            scheduler.weight_priority = weight_priority

        if weight_weather is not None:
            scheduler.weight_weather = weight_weather

        # Optional jetty balancing term
        try:
            if weight_jetty_balance is not None and float(weight_jetty_balance) > 0.0:
                setattr(scheduler, 'weight_jetty_balance', float(weight_jetty_balance))
        except Exception:
            pass

        # Run optimization
        optimized_schedule = scheduler.optimize()

        # Log optimization start/completion for analytics
        try:
            optimization_summary = {
                'vessels_input': len(planning_vessels),
                'assignments_created': len(optimized_schedule.assignments),
                'horizon_days': horizon_days,
                'time_granularity_hours': time_granularity_hours,
                'weights': {
                    'throughput': weight_throughput,
                    'demurrage': weight_demurrage,
                    'priority': weight_priority,
                    'weather': weight_weather
                },
                'parameters': {
                    'force_assign_all': force_assign_all,
                    'preserve_locked': preserve_locked,
                    'approach_time_hours': approach_time_hours,
                    'free_wait_buffer_hours': free_wait_buffer_hours
                }
            }
            
            # Log optimization completion
            from datetime import timedelta
            db.log_assignment_change(
                assignment_id=0,  # System-level event
                old_start_time=None,
                old_end_time=None,
                new_start_time=datetime.now(timezone.utc).isoformat(),
                new_end_time=(datetime.now(timezone.utc) + timedelta(days=horizon_days)).isoformat(),
                reason=f"Optimization completed: {len(optimized_schedule.assignments)} assignments created from {len(planning_vessels)} vessels",
                vessel_id="SYSTEM",
                vessel_name="Optimization Engine",
                jetty_name=None,
                changed_by="system",
                terminal_id=db.get_active_terminal_id()
            )
            
            # Log detailed optimization analysis
            db.log_change_analysis(
                assignment_id=0,
                change_type='optimization_completed',
                reason_text=f"Optimization run completed with {len(optimized_schedule.assignments)} assignments",
                original_value=str(len(planning_vessels)) + " unscheduled vessels",
                new_value=f"{len(optimized_schedule.assignments)} scheduled assignments",
                vessel_id="SYSTEM",
                vessel_name="Optimization Engine",
                changed_by="system",
                terminal_id=db.get_active_terminal_id()
            )
        except Exception as log_e:
            logger.warning(f"Failed to log optimization completion: {log_e}")

        # Update the schedule (in-memory)
        state["schedule"] = optimized_schedule
        state["last_optimization_time"] = datetime.now(timezone.utc)

        # Optionally fill remaining feasible vessels greedily for demo presets
        try:
            if fill_unassigned:
                from datetime import timedelta
                # Build per-jetty timeline of occupied windows from current schedule and reserved intervals
                occupied: Dict[str, List[Tuple[datetime, datetime]]] = {}
                for a in getattr(optimized_schedule, 'assignments', []):
                    jn = getattr(a.jetty, 'name', getattr(a.jetty, 'id', ''))
                    occupied.setdefault(jn, []).append((a.start_time, a.end_time))
                # Add reserved windows
                for j in getattr(scheduler.terminal, 'jetties', []) or []:
                    jn = getattr(j, 'name', '')
                    for (rs, re) in (scheduler.reserved_intervals_by_jetty or {}).get(jn, []):
                        occupied.setdefault(jn, []).append((rs, re))

                # Helper: find earliest feasible start >= ebr, non-overlapping
                def find_slot(jn: str, ebr: datetime, duration: timedelta) -> Optional[Tuple[datetime, datetime]]:
                    windows = sorted(occupied.get(jn, []))
                    t = max(ebr, scheduler.start_time)
                    # Try gaps between windows
                    for (ws, we) in windows:
                        if t + duration <= ws:
                            return t, t + duration
                        t = max(t, we)
                        if t < scheduler.start_time:
                            t = scheduler.start_time
                        if t > scheduler.end_time:
                            break
                    # After last window
                    if t + duration <= scheduler.end_time:
                        return t, t + duration
                    return None

                # Determine unassigned relevant vessels
                assigned_ids = {getattr(a.vessel, 'id') for a in optimized_schedule.assignments}
                relevant = [v for v in state["vessels"] if normalize_status(getattr(v, 'status', '')) in ["EN_ROUTE", "APPROACHING", "ARRIVED", "WAITING"] and getattr(v, 'id') not in assigned_ids]

                # Try to place each vessel on any compatible jetty
                for v in relevant:
                    # Compatible jetties via terminal rules
                    prods = set()
                    try:
                        prods = v.get_cargo_products()
                    except Exception:
                        prods = set()
                    vessel_type_str = "seagoing" if getattr(v, 'vessel_type', None) == VesselType.TANKER else "inland_barge"
                    compat = []
                    for p in (prods or {"hydrocarbons"}):
                        try:
                            compat.extend(scheduler.terminal.find_compatible_jetties(v.length, v.draft, v.deadweight, p, vessel_type_str))
                        except Exception:
                            continue
                    # Deduplicate
                    seen = set()
                    compat = [j for j in compat if not (getattr(j, 'id', None) in seen or seen.add(getattr(j, 'id', None)))]
                    if not compat:
                        continue

                    # Predicted duration using ML
                    best = None
                    for j in compat:
                        try:
                            features = scheduler.ml_service.extract_features(v, j)
                            ml = scheduler.ml_service.predict_times(features)
                            dur = ml.terminal_time
                        except Exception:
                            dur = v.estimated_operation_time(j.max_flow_rate)

                        # EBR from ETA + approach
                        ebr = getattr(v, 'eta', None)
                        if ebr:
                            ebr = ebr + timedelta(hours=int(scheduler.approach_time_hours))
                        else:
                            ebr = scheduler.start_time

                        slot = find_slot(getattr(j, 'name', getattr(j, 'id', '')), ebr, dur)
                        if slot:
                            s, e = slot
                            # Record candidate
                            best = (j, s, e, dur)
                            break  # take first feasible for speed
                    if best:
                        j, s, e, dur = best
                        # Create and add assignment
                        from src.models.schedule import Assignment
                        a = Assignment(
                            id=f"A{len(optimized_schedule.assignments):04d}",
                            jetty=j,
                            vessel=v,
                            start_time=s,
                            end_time=e,
                            status="PENDING_APPROVAL"
                        )
                        try:
                            # apply ML if available
                            features = scheduler.ml_service.extract_features(v, j)
                            preds = scheduler.ml_service.predict_times(features)
                            a.apply_ml_predictions(preds, getattr(j, 'id', None))
                        except Exception:
                            pass
                        optimized_schedule.add_assignment(a)
                        # Update occupied timeline
                        jn = getattr(j, 'name', getattr(j, 'id', ''))
                        occupied.setdefault(jn, []).append((s, e))
        except Exception as fill_err:
            logger.warning(f"fill_unassigned failed: {fill_err}")

        # Persist assignments to database for UI endpoints that read from DB
        try:
            terminal_id = db.get_active_terminal_id()
            # Transform schedule assignments to DB rows
            db_rows = []
            for a in optimized_schedule.assignments:
                # Jetty name expected by DB is the display/name field
                jetty_name = getattr(a.jetty, 'name', None) or getattr(a.jetty, 'id', '')
                vessel_name = getattr(a.vessel, 'name', '')
                vessel_type = getattr(a.vessel.vessel_type, 'value', getattr(a.vessel, 'vessel_type', 'tanker'))
                db_rows.append({
                    'vessel_id': getattr(a.vessel, 'id'),
                    'vessel_name': vessel_name,
                    'vessel_type': vessel_type if isinstance(vessel_type, str) else str(vessel_type),
                    'jetty_name': jetty_name,
                    'start_time': a.start_time.isoformat(),
                    'end_time': a.end_time.isoformat(),
                    'status': a.status or 'SCHEDULED',
                })

            # Replace all assignments for terminal with new optimized ones and get database IDs
            created_assignments = db.replace_assignments(db_rows, terminal_id)
            logger.info(f"Persisted {len(created_assignments)} optimized assignments to database for terminal {terminal_id}")

            # Log nominations becoming planned after assignments are created (so we have database IDs)
            try:
                newly_planned_count = db.log_nominations_to_planned(created_assignments, terminal_id)
                if newly_planned_count > 0:
                    logger.info(f"Logged {newly_planned_count} vessels transitioning from nomination to planned")
            except Exception as log_err:
                logger.warning(f"Failed to log nomination to planned transitions: {log_err}")

            # Update vessel statuses to reflect they are now planned (remove from unscheduled)
            try:
                assigned_vessel_ids = {row['vessel_id'] for row in db_rows}
                vessels_to_update = [v for v in state["vessels"] if getattr(v, 'id') in assigned_vessel_ids]
                
                for vessel in vessels_to_update:
                    # Update vessel status to indicate it's now planned/scheduled
                    old_status = vessel.status
                    vessel.status = "WAITING"  # or "DOCKED" depending on your business logic
                    logger.debug(f"Updated vessel {vessel.id} status from {old_status} to {vessel.status} (now planned)")
                
                if vessels_to_update:
                    logger.info(f"Updated status for {len(vessels_to_update)} vessels that were planned")
                    
            except Exception as status_err:
                logger.warning(f"Failed to update vessel statuses after planning: {status_err}")

            # Determine unassigned vessels and enhanced reasons from scheduler analysis
            try:
                from src.utils.status_utils import normalize_status
            except Exception:
                def normalize_status(s):
                    return (s or "").upper()

            assigned_vessel_ids = {getattr(a.vessel, 'id') for a in optimized_schedule.assignments}
            relevant_vessels = [v for v in state["vessels"] if normalize_status(getattr(v, 'status', '')) in ["EN_ROUTE", "APPROACHING", "ARRIVED", "WAITING"]]

            unassigned = []
            for v in relevant_vessels:
                if getattr(v, 'id') in assigned_vessel_ids:
                    continue

                # Try to get enhanced reason from scheduler's unassignment analysis
                reason = "Not optimal to assign"
                try:
                    # Use the scheduler's enhanced analysis if available
                    if hasattr(scheduler, '_analyze_unassignment_reasons'):
                        # Get the solver and variables from the scheduler if available
                        solver_vars = getattr(scheduler, '_last_solver_state', None)
                        if solver_vars:
                            solver, assignment_vars, start_vars, end_vars = solver_vars
                            reasons = scheduler._analyze_unassignment_reasons(v, solver, assignment_vars, start_vars, end_vars, relevant_vessels)
                            reason = '; '.join(reasons) if reasons else reason
                        else:
                            # Fallback to basic analysis
                            if v.eta and v.eta > scheduler.end_time:
                                reason = "ETA outside planning horizon"
                            elif not force_assign_all:
                                reason = "Not optimal to assign (enable Force Assign All to override)"
                    else:
                        # Fallback to original basic analysis
                        if v.eta and v.eta > scheduler.end_time:
                            reason = "ETA outside planning horizon"
                        else:
                            # Compute compatibility by dimensions and products
                            candidate_jetties = []
                            if getattr(v, 'vessel_type', None) and getattr(v.vessel_type, 'value', None) == 'tanker':
                                candidate_jetties = getattr(terminal, 'vessel_berths', [])
                            else:
                                candidate_jetties = getattr(terminal, 'barge_berths', [])

                            dim_compatible = []
                            for j in candidate_jetties:
                                try:
                                    if v.is_compatible_with_jetty(j.max_length, j.max_draft, j.max_deadweight):
                                        dim_compatible.append(j)
                                except Exception:
                                    continue
                            if not dim_compatible:
                                reason = "No jetty fits vessel dimensions"
                            else:
                                prods = set()
                                try:
                                    prods = v.get_cargo_products()
                                except Exception:
                                    prods = set()
                                product_compatible = []
                                for j in dim_compatible:
                                    try:
                                        if any(j.can_handle_product(p) for p in prods) or not prods:
                                            product_compatible.append(j)
                                    except Exception:
                                        continue
                                if not product_compatible:
                                    reason = "No jetty supports cargo products"
                                else:
                                    reason = "Time conflicts within horizon"
                except Exception as e:
                    logger.warning(f"Failed to analyze unassignment reason for vessel {getattr(v, 'id', '?')}: {e}")
                    reason = "Could not determine reason"

                unassigned.append({
                    "vessel_id": getattr(v, 'id'),
                    "vessel_name": getattr(v, 'name', ''),
                    "reason": reason
                })

            # Record result summary for status endpoint/UI
            state["last_optimization_result"] = {
                "assigned_count": len(db_rows),
                "objective_value": optimized_schedule.objective_value,
                "message": "Optimization completed successfully" if db_rows else "No feasible assignments found",
                "unassigned": unassigned,
                "breakdown": getattr(optimized_schedule, 'metadata', {}).get('objective_breakdown')
            }
        except Exception as persist_err:
            logger.error(f"Failed to persist optimized schedule to database: {persist_err}")
            state["last_optimization_result"] = {
                "assigned_count": 0,
                "objective_value": optimized_schedule.objective_value,
                "message": f"Optimization finished but failed to persist: {persist_err}",
                "breakdown": getattr(optimized_schedule, 'metadata', {}).get('objective_breakdown')
            }

        logger.info(f"Optimization completed with objective value: {optimized_schedule.objective_value}")
    except Exception as e:
        logger.error(f"Error during optimization: {e}")
        state["last_optimization_result"] = {
            "assigned_count": 0,
            "objective_value": None,
            "message": f"Optimization error: {e}",
        }
    finally:
        # Set optimization as not in progress
        state["optimization_in_progress"] = False


@app.get("/api/assignment-status-job/status")
async def get_assignment_status_job_status():
    """Get assignment status job status and metrics"""
    global assignment_status_job, scheduler
    
    if assignment_status_job is None:
        return {
            "status": "not_initialized",
            "message": "Assignment status job not initialized"
        }
    
    job_status = assignment_status_job.get_job_status()
    
    # Add scheduler status
    if scheduler:
        job_status["scheduler_running"] = scheduler.running
        # Get next run time for the job
        try:
            job = scheduler.get_job('assignment_status_transitions')
            if job:
                job_status["next_run_time"] = job.next_run_time.isoformat() if job.next_run_time else None
        except Exception:
            job_status["next_run_time"] = None
    else:
        job_status["scheduler_running"] = False
        job_status["next_run_time"] = None
    
    return job_status


@app.post("/api/assignment-status-job/trigger")
async def trigger_assignment_status_job():
    """Manually trigger the assignment status job"""
    global assignment_status_job
    
    if assignment_status_job is None:
        raise HTTPException(status_code=503, detail="Assignment status job not initialized")
    
    try:
        result = await assignment_status_job.run_transition_job()
        return result
    except Exception as e:
        logger.error(f"Error triggering assignment status job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger job: {e}")


def start_api_server(host: str = "127.0.0.1", port: int = 7000, debug: bool = True):
    """Start the FastAPI server"""
    # Configure logging
    log_level = "info"
    if debug:
        log_level = "debug"

    # Start server
    uvicorn.run(app, host=host, port=port, log_level=log_level)

    return app


# Settings models
class SettingUpdate(BaseModel):
    value: str

class SettingsUpdate(BaseModel):
    settings: Dict[str, Any]

@app.get("/api/settings")
async def get_all_settings(category: Optional[str] = None):
    """Get all settings or settings for a specific category"""
    try:
        settings = db.get_settings(category)
        # Filter out sensitive settings
        return [s for s in settings if not s['is_sensitive']]
    except Exception as e:
        logger.error(f"Error getting settings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/settings/{key}")
async def get_setting(key: str):
    """Get a single setting by key"""
    try:
        setting = db.get_setting(key)
        if not setting:
            raise HTTPException(status_code=404, detail=f"Setting {key} not found")
        if setting['is_sensitive']:
            raise HTTPException(status_code=403, detail="Access to sensitive setting denied")
        return setting
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting setting {key}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/settings/{key}")
async def update_setting(key: str, setting: SettingUpdate):
    """Update a single setting"""
    try:
        # Check if setting exists and is not sensitive
        existing = db.get_setting(key)
        if not existing:
            raise HTTPException(status_code=404, detail=f"Setting {key} not found")
        if existing['is_sensitive']:
            raise HTTPException(status_code=403, detail="Cannot update sensitive setting via API")
        
        success = db.update_setting(key, setting.value)
        if not success:
            raise HTTPException(status_code=400, detail=f"Failed to update setting {key}")
        return {"message": f"Setting {key} updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating setting {key}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/settings")
async def update_multiple_settings(settings_update: SettingsUpdate):
    """Update multiple settings at once"""
    try:
        # Check if any setting is sensitive; create if missing (non-sensitive)
        for key, value in settings_update.settings.items():
            existing = db.get_setting(key)
            if existing and existing.get('is_sensitive'):
                raise HTTPException(status_code=403, detail=f"Cannot update sensitive setting {key} via API")
            if not existing:
                # Create setting if it doesn't exist
                created = db.create_setting(key, str(value), is_sensitive=False)
                if not created:
                    raise HTTPException(status_code=400, detail=f"Failed to create setting {key}")
        
        success = db.update_settings(settings_update.settings)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to update settings")
        
        # Apply AIS-related settings immediately if provided
        try:
            ais_client = global_state.get("aisstream")
            terminal = global_state.get("terminal")
            if ais_client and not ais_client.use_mock:
                # Update radius/bbox
                if 'ais_radius_km' in settings_update.settings:
                    try:
                        radius_km = float(settings_update.settings['ais_radius_km'])
                    except Exception:
                        radius_km = 100.0
                    if terminal and hasattr(terminal, 'location'):
                        lat, lon = terminal.location
                    else:
                        lat, lon = (51.34543250288062, 3.751466718019277)
                    ais_client.set_subscription_region(lat, lon, radius_km=radius_km)
                # Handle preload toggle
                if 'ais_preload' in settings_update.settings:
                    preload_val = str(settings_update.settings['ais_preload']).lower() in ("1", "true", "yes")
                    if preload_val and not ais_client.connection_active:
                        logger.info("Settings change: starting AIS continuous streaming")
                        ais_client.start_streaming_continuous()
                    if not preload_val and ais_client.connection_active:
                        logger.info("Settings change: stopping AIS streaming per user setting")
                        ais_client.stop_streaming()
        except Exception as e:
            logger.warning(f"Failed to apply AIS settings immediately: {e}")
        
        return {"message": "Settings updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# ======================== OPTIMIZATION PRESETS API ========================

class OptimizationPresets(BaseModel):
    """Model for optimization presets configuration"""
    throughput: Dict[str, Any] = None
    cost: Dict[str, Any] = None
    infrastructure: Dict[str, Any] = None
    balanced: Dict[str, Any] = None

# Default optimization presets
DEFAULT_OPTIMIZATION_PRESETS = {
    "throughput": {
        "name": "🚀 Maximum Throughput",
        "description": "Prioritizes maximizing the number of vessels processed within the planning horizon. Ideal for high-demand periods when throughput is critical.",
        "details": {"throughput": "High", "cost": "Medium", "infrastructure": "Medium"},
        "weights": {
            "throughput": 15.0,
            "demurrage": 3.0,
            "priority": 5.0,
            "utilization": 2.0,
            "horizon": 7,
            "granularity": 1
        }
    },
    "cost": {
        "name": "💰 Cost Efficiency",
        "description": "Focuses on minimizing demurrage costs and operational expenses. Best for cost-controlled operations.",
        "details": {"throughput": "Medium", "cost": "High", "infrastructure": "Medium"},
        "weights": {
            "throughput": 8.0,
            "demurrage": 18.0,
            "priority": 2.0,
            "utilization": 3.0,
            "horizon": 14,
            "granularity": 2
        }
    },
    "infrastructure": {
        "name": "🏗️ Infrastructure Efficiency",
        "description": "Optimizes jetty utilization and minimizes infrastructure conflicts. Ideal for maximizing facility efficiency.",
        "details": {"throughput": "Medium", "cost": "Medium", "infrastructure": "High"},
        "weights": {
            "throughput": 10.0,
            "demurrage": 8.0,
            "priority": 6.0,
            "utilization": 15.0,
            "horizon": 10,
            "granularity": 1
        }
    },
    "balanced": {
        "name": "⚖️ Balanced",
        "description": "Balanced approach across all optimization factors. Good general-purpose optimization strategy.",
        "details": {"throughput": "Medium", "cost": "Medium", "infrastructure": "Medium"},
        "weights": {
            "throughput": 12.0,
            "demurrage": 10.0,
            "priority": 8.0,
            "utilization": 8.0,
            "horizon": 7,
            "granularity": 1
        }
    }
}

@app.get("/api/settings/optimization-presets")
async def get_optimization_presets():
    """Get optimization presets configuration"""
    try:
        # Try to get from database first
        presets_setting = db.get_setting("optimization_presets")
        if presets_setting and presets_setting.get("value"):
            try:
                import json
                stored_presets = json.loads(presets_setting["value"])
                # Merge with defaults to ensure all presets exist
                result = {**DEFAULT_OPTIMIZATION_PRESETS}
                result.update(stored_presets)
                return result
            except (json.JSONDecodeError, TypeError):
                logger.warning("Failed to parse stored optimization presets, using defaults")
        
        # Return defaults if not found or parsing failed
        return DEFAULT_OPTIMIZATION_PRESETS
        
    except Exception as e:
        logger.error(f"Error getting optimization presets: {str(e)}")
        # Return defaults on any error
        return DEFAULT_OPTIMIZATION_PRESETS

@app.put("/api/settings/optimization-presets")
async def update_optimization_presets(presets: Dict[str, Any]):
    """Update optimization presets configuration"""
    try:
        import json
        
        # Validate that the required preset keys exist
        required_presets = ["throughput", "cost", "infrastructure", "balanced"]
        for preset_key in required_presets:
            if preset_key not in presets:
                raise HTTPException(status_code=400, detail=f"Missing required preset: {preset_key}")
        
        # Validate preset structure
        for preset_key, preset_data in presets.items():
            if not isinstance(preset_data, dict):
                raise HTTPException(status_code=400, detail=f"Preset {preset_key} must be an object")
            
            required_fields = ["name", "description", "details", "weights"]
            for field in required_fields:
                if field not in preset_data:
                    raise HTTPException(status_code=400, detail=f"Preset {preset_key} missing required field: {field}")
        
        # Store in database
        presets_json = json.dumps(presets)
        success = db.update_setting("optimization_presets", presets_json, is_sensitive=False)
        
        if not success:
            # Try to create the setting if it doesn't exist
            success = db.create_setting("optimization_presets", presets_json, is_sensitive=False)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to save optimization presets")
        
        return {"message": "Optimization presets updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating optimization presets: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# AI Assistant API endpoints
class AIQueryRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None
    type: str = "general"


@app.post("/api/ai/query")
async def ai_query_api(
    request: AIQueryRequest,
    state: Dict = Depends(get_data)
):
    """Query the AI assistant for optimization guidance"""
    try:
        # Get AI interface (would need to be initialized with API key from settings)
        from src.llm_interface.claude_interface import ClaudeInterface
        
        # Try to get API key from settings or environment
        ai_api_key = os.getenv("CLAUDE_API_KEY", "TEST")  # Use TEST for mock responses
        ai_interface = ClaudeInterface(ai_api_key)
        
        # Prepare context for AI
        context = request.context or {}
        
        # Add system context if available
        if "vessels" in state:
            context["vessels"] = state["vessels"]
        if "terminal" in state:
            context["terminal"] = state["terminal"]
        
        # Add optimization-specific context for optimization guidance
        if request.type == "optimization_guidance":
            # Enhance prompt for optimization guidance
            enhanced_message = f"""
            The user is asking about optimization strategy selection. They want guidance on choosing the right optimization preset for their terminal operations.
            
            User question: {request.message}
            
            Current context:
            - Current preset: {context.get('current_preset', 'None selected')}
            - Current parameters: {context.get('parameters', {})}
            
            Available presets:
            1. 🚀 Maximum Throughput - Best for high-demand periods, maximizes vessel processing
            2. 💰 Cost Efficiency - Ideal for minimizing demurrage and operational costs  
            3. 🏗️ Infrastructure Efficiency - Optimizes jetty utilization and facility efficiency
            4. ⚖️ Balanced - General-purpose strategy balancing all factors
            
            Please provide specific guidance on which preset would work best for their situation and explain why. If appropriate, suggest specific parameter adjustments.
            """
            
            response = ai_interface.query(enhanced_message, context)
            
            # Try to detect if AI suggested a specific preset
            suggested_preset = None
            if "maximum throughput" in response.lower() or "throughput" in response.lower():
                suggested_preset = "🚀 Maximum Throughput"
            elif "cost efficiency" in response.lower() or "cost" in response.lower():
                suggested_preset = "💰 Cost Efficiency"
            elif "infrastructure efficiency" in response.lower() or "infrastructure" in response.lower():
                suggested_preset = "🏗️ Infrastructure Efficiency"
            elif "balanced" in response.lower():
                suggested_preset = "⚖️ Balanced"
            
            return {
                "success": True,
                "response": response,
                "suggested_preset": suggested_preset,
                "type": request.type
            }
        else:
            # General query
            response = ai_interface.query(request.message, context)
            return {
                "success": True,
                "response": response,
                "type": request.type
            }
        
    except Exception as e:
        logger.error(f"Error in AI query: {str(e)}")
        # Return a helpful fallback response
        fallback_response = """
        I'm having trouble connecting to the AI service right now. Here's some general guidance about optimization presets:

        **🚀 Maximum Throughput**: Choose this when you need to process as many vessels as possible in the shortest time. Best for peak demand periods.

        **💰 Cost Efficiency**: Select this to minimize demurrage costs and operational expenses. Ideal when budget control is priority.

        **🏗️ Infrastructure Efficiency**: Use this to maximize jetty utilization and facility efficiency. Good for optimizing resource usage.

        **⚖️ Balanced**: A good general-purpose choice that considers all factors equally. Start here if you're unsure.

        You can always fine-tune the parameters after selecting a preset to match your specific needs.
        """
        
        return {
            "success": False,
            "response": fallback_response,
            "error": "AI service temporarily unavailable",
            "type": request.type
        }


@app.post("/api/reset-data")
async def reset_all_data_api():
    """Reset all data to initial state"""
    try:
        # Get active terminal
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            return {
                "success": False,
                "message": "No active terminal configured"
            }

        # Clear all assignments
        try:
            assignments = db.get_assignments(terminal_id)
            for assignment in assignments:
                db.delete_assignment(assignment.get('id'))
        except Exception as e:
            logger.warning(f"Error clearing assignments: {e}")

        # Clear all vessels (except those in database)
        try:
            vessels = db.get_vessels(terminal_id)
            # Note: We don't delete vessels from database as they might be master data
            # Only clear in-memory vessels through the application state
        except Exception as e:
            logger.warning(f"Error clearing vessels: {e}")

        # Reset settings to defaults (this will restore original settings)
        try:
            # Delete all current settings for the terminal
            conn = db.get_connection()
            cursor = conn.cursor()
            cursor.execute('DELETE FROM settings WHERE terminal_id = ?', (terminal_id,))
            conn.commit()

            # Re-insert default settings (this will be done by db.init_db() but let's trigger it)
            # Actually, the init_db method should handle this when called
            db.init_db()

        except Exception as e:
            logger.warning(f"Error resetting settings: {e}")

        return {
            "success": True,
            "message": "All data has been reset to initial state",
            "reset_actions": [
                "Cleared all assignments",
                "Reset vessels to database state",
                "Restored default settings"
            ]
        }

    except Exception as e:
        logger.error(f"Error resetting all data: {e}")
        return {
            "success": False,
            "message": f"Error resetting data: {str(e)}"
        }


@app.post("/api/schedule/reset")
async def reset_schedule_api(state: Dict = Depends(get_data)):
    """Reset the schedule to its original state"""
    try:
        # Check if we have the original schedule
        if not state.get("original_schedule"):
            # If no original schedule saved, return an error
            return {
                "success": False,
                "message": "No original schedule available to reset to"
            }
        
        # Create a new schedule based on the original schedule
        original = state["original_schedule"]
        new_schedule = Schedule(
            start_time=original.start_time,
            end_time=original.end_time,
            objective_value=original.objective_value
        )
        
        # Copy all assignments from the original schedule
        for assignment in original.assignments:
            new_assignment = Assignment(
                id=assignment.id,
                jetty=assignment.jetty,
                vessel=assignment.vessel,
                start_time=assignment.start_time,
                end_time=assignment.end_time,
                status=assignment.status,
                actual_start_time=assignment.actual_start_time,
                actual_end_time=assignment.actual_end_time,
                surveyor_ids=assignment.surveyor_ids.copy() if assignment.surveyor_ids else [],
                pump_ids=assignment.pump_ids.copy() if assignment.pump_ids else [],
                tank_ids=assignment.tank_ids.copy() if assignment.tank_ids else [],
                notes=assignment.notes,
                metadata={k: v for k, v in assignment.metadata.items()} if assignment.metadata else {}
            )
            new_schedule.add_assignment(new_assignment)
        
        # Update the current schedule with the reset one
        state["schedule"] = new_schedule
        
        # Reset the optimization timestamp
        state["last_optimization_time"] = None
        
        return {
            "success": True,
            "message": "Schedule has been reset to its original state",
            "assignment_count": len(new_schedule.assignments)
        }
    except Exception as e:
        logger.error(f"Error resetting schedule: {e}")
        return {
            "success": False,
            "message": f"Error resetting schedule: {str(e)}"
        }

 
@app.post("/api/schedule/reset")
async def reset_schedule_api(state: Dict = Depends(get_data)):
    """Reset the schedule to its original state"""
    try:
        # Check if we have the original schedule
        if not state.get("original_schedule"):
            # If no original schedule saved, return an error
            return {
                "success": False,
                "message": "No original schedule available to reset to"
            }
        
        # Create a new schedule based on the original schedule
        original = state["original_schedule"]
        new_schedule = Schedule(
            start_time=original.start_time,
            end_time=original.end_time,
            objective_value=original.objective_value
        )
        
        # Copy all assignments from the original schedule
        for assignment in original.assignments:
            new_assignment = Assignment(
                id=assignment.id,
                jetty=assignment.jetty,
                vessel=assignment.vessel,
                start_time=assignment.start_time,
                end_time=assignment.end_time,
                status=assignment.status,
                actual_start_time=assignment.actual_start_time,
                actual_end_time=assignment.actual_end_time,
                surveyor_ids=assignment.surveyor_ids.copy() if assignment.surveyor_ids else [],
                pump_ids=assignment.pump_ids.copy() if assignment.pump_ids else [],
                tank_ids=assignment.tank_ids.copy() if assignment.tank_ids else [],
                notes=assignment.notes,
                metadata={k: v for k, v in assignment.metadata.items()} if assignment.metadata else {}
            )
            new_schedule.add_assignment(new_assignment)
        
        # Update the current schedule with the reset one
        state["schedule"] = new_schedule
        
        # Reset the optimization timestamp
        state["last_optimization_time"] = None
        
        return {
            "success": True,
            "message": "Schedule has been reset to its original state",
            "assignment_count": len(new_schedule.assignments)
        }
    except Exception as e:
        logger.error(f"Error resetting schedule: {e}")
        return {
            "success": False,
            "message": f"Error resetting schedule: {str(e)}"
        }


# ================================
# Analytics API Endpoints
# ================================

@app.get("/api/analytics/overview")
async def get_analytics_overview(
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format")
):
    """Get analytics overview data for the specified date range"""
    try:
        # Parse dates and make end inclusive (end of day)
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get analytics overview from database
        overview = db.get_analytics_overview(start_dt, end_dt)
        
        return {
            "success": True,
            "data": overview,
            "date_range": {
                "start": start_date,
                "end": end_date
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting analytics overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/analytics/ml-performance")
async def get_ml_performance(
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format")
):
    """Get ML prediction performance data for the specified date range"""
    try:
        # Parse dates and make end inclusive (end of day)
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get ML performance data from database
        ml_data = db.get_ml_performance_data(start_dt, end_dt)
        
        return {
            "success": True,
            "data": ml_data,
            "date_range": {
                "start": start_date,
                "end": end_date
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting ML performance data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/analytics/changes")
async def get_change_analysis(
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format")
):
    """Get change analysis data for the specified date range"""
    try:
        # Parse dates and make end inclusive (end of day)
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get change analysis data from database
        change_data = db.get_change_analysis_data(start_dt, end_dt)
        
        return {
            "success": True,
            "data": change_data,
            "date_range": {
                "start": start_date,
                "end": end_date
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting change analysis data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/analytics/efficiency")
async def get_efficiency_metrics(
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format")
):
    """Get planning efficiency metrics for the specified date range"""
    try:
        # Parse dates and make end inclusive (end of day)
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get efficiency metrics from database
        efficiency_data = db.get_efficiency_metrics(start_dt, end_dt)
        
        return {
            "success": True,
            "data": efficiency_data,
            "date_range": {
                "start": start_date,
                "end": end_date
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting efficiency metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    start_api_server(debug=True)
