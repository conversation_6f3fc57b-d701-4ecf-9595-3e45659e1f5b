import os
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from .models import Base

# Database configuration - PostgreSQL only
DATABASE_URL = os.getenv("DATABASE_URL")

def get_database_url() -> str:
    """Get the PostgreSQL database URL from environment"""
    if DATABASE_URL:
        return DATABASE_URL
    
    # Compose from individual components if DATABASE_URL not provided
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = os.getenv("DB_PORT", "7432")
    db_name = os.getenv("DB_NAME", "planner")
    db_user = os.getenv("DB_USER", "postgres")
    db_password = os.getenv("DB_PASSWORD", "")
    
    # Auto-detect if we're running inside Docker
    # If DB_HOST is host.docker.internal but we're not in Docker, use localhost
    if db_host == "host.docker.internal" and not os.path.exists("/.dockerenv"):
        db_host = "localhost"
    
    return f"postgresql+psycopg2://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def create_database_engine():
    """Create PostgreSQL database engine"""
    url = get_database_url()
    
    # PostgreSQL configuration
    engine = create_engine(
        url,
        pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
        max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
        pool_timeout=int(os.getenv("DB_POOL_TIMEOUT", "30")),
        pool_recycle=int(os.getenv("DB_POOL_RECYCLE", "3600")),
        echo=os.getenv("DB_ECHO", "false").lower() == "true"
    )
    
    return engine

# Create global engine and session factory
engine = create_database_engine()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Create all tables in the database"""
    Base.metadata.create_all(bind=engine)

def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_session() -> Session:
    """Get a database session (for use outside FastAPI)"""
    return SessionLocal()